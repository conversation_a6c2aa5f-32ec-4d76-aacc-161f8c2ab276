# coding=utf-8
from citext import CIText
from sqlalchemy import Column, String, Boolean, DateTime, ARRAY, ForeignKey, Date, Index, func, UniqueConstraint
from sqlalchemy.dialects.postgresql import (
    BIGINT, INTEGER, NUMERIC, TEXT, TIMESTAMP, JSON, JSONB,
)
from sqlalchemy_json import mutable_json_type
from sqlalchemy_utils import TSVectorType

from .base import Base, TableName
from .user import User


class CacheyCIText(CIText):
    # Workaround for https://github.com/mahmoudimus/sqlalchemy-citext/issues/25
    # Can remove when that issue is fixed
    cache_ok = True


class Issue(TableName, Base):
    id = Column(BIGINT, primary_key=True, autoincrement=False)
    key = Column(String, nullable=False, index=True)
    parent_id = Column(
        BIGINT,
        ForeignKey(column="issue.id", deferrable=True, initially='DEFERRED'),
        nullable=True,
    )
    parent_key = Column(
        String,
        ForeignKey(column="issue.key", deferrable=True, initially='DEFERRED'),
        nullable=True,
    )
    summary = Column(String, nullable=False)
    # Source: https://amercader.net/blog/beware-of-json-fields-in-sqlalchemy/
    description = Column(JSON)
    description_markdown = Column(TEXT)
    # https://stackoverflow.com/questions/13837111/tsvector-in-sqlalchemy
    tscv_summary_description = Column(TSVectorType, nullable=False)

    isSubTask = Column(Boolean, nullable=False, index=True)
    issuetype = Column(String, nullable=False, index=True)
    issue_hierarchy_level = Column(INTEGER, nullable=True)
    status = Column(String, nullable=False, index=True)
    statusCategory = Column(String, nullable=False)
    statuscategorychangedate = Column(DateTime(timezone=True), nullable=True, )
    resolution = Column(String)
    resolutiondate = Column(DateTime(timezone=True))
    priority = Column(String, nullable=True)
    urgency = Column(String, nullable=True)
    components = Column(ARRAY(String), nullable=True)
    fixVersions = Column(ARRAY(String), nullable=True, index=True)
    versions = Column(ARRAY(String), nullable=True, index=True)
    assignee = Column(String, ForeignKey(User.accountId), nullable=True)
    reporter = Column(String, ForeignKey(User.accountId), nullable=False)
    created = Column(DateTime(timezone=True), nullable=False, index=True)
    updated = Column(DateTime(timezone=True), nullable=False, index=True)
    Rank = Column(String, nullable=False)
    # sprintid = Column(ARRAY(BIGINT), nullable=True)
    sprint = Column(JSON, nullable=True)
    Team = Column(String, nullable=True)
    ClientJira = Column(ARRAY(String), nullable=True)
    startdate = Column(Date, nullable=True)
    duedate = Column(Date, nullable=True)
    timetracking = Column(mutable_json_type(dbtype=JSONB, nested=True), nullable=True)
    # originalestimate = Column(NUMERIC)
    timeoriginalestimate = Column(NUMERIC)
    timespent = Column(NUMERIC)
    aggregatetimeoriginalestimate = Column(NUMERIC)
    aggregatetimeestimate = Column(NUMERIC)
    aggregatetimespent = Column(NUMERIC)
    timeestimate = Column(NUMERIC)
    progress_progress = Column(NUMERIC)
    progress_total = Column(NUMERIC)
    progress_percent = Column(NUMERIC)
    aggregateprogress_progress = Column(NUMERIC)
    aggregateprogress_total = Column(NUMERIC)
    aggregateprogress_percent = Column(NUMERIC)
    totaleffort = Column(NUMERIC)
    totaldeveffort = Column(NUMERIC)
    baeffort = Column(NUMERIC)
    adeffort = Column(NUMERIC)
    rdeffort = Column(NUMERIC)
    qaeffort = Column(NUMERIC)
    contingency = Column(NUMERIC)
    storypoints = Column(BIGINT, nullable=True)
    testcaseno = Column(String)
    testcasesuite = Column(String)
    teststepno = Column(String)
    scenariono = Column(String)
    reqfinalized = Column(String)
    approvalstatus = Column(String)
    reopen_count = Column(BIGINT)
    # path = Column(LtreeType, nullable=True)
    # path_id = Column(LtreeType, nullable=True)
    # parent_path = Column(LtreeType, nullable=True)
    # parent_path_id = Column(LtreeType, nullable=True)
    qc_check = Column(TEXT, nullable=True)
    cvss_score = Column(NUMERIC, nullable=True)
    initiated_by = Column(TEXT, nullable=True)
    change_risk = Column(TEXT, nullable=True)
    category_type = Column(TEXT, nullable=True)
    severity = Column(TEXT, nullable=True)
    # linked_issues = Column(ARRAY(CacheyCIText()), nullable=True)
    initiative_detail = Column(TEXT, nullable=True, index=True)

    __table_args__ = (
        UniqueConstraint("key", name="uq_issue_issue_key"),
        Index(
            "idx_issue_fixVersions", fixVersions, postgresql_using='gin'
        ),
        Index(
            "idx_issue_versions", versions, postgresql_using='gin'
        ),
        Index(
            "idx_tsv_summary_description", tscv_summary_description,
            postgresql_using='gin'
        ),
        {'schema': None},
    )


class IssueComments(Base):
    use_snake_case = True
    __table_args__ = {
        # 'postgresql_partition_by': 'RANGE (created)',  # Optional, if you are using range partitioning
        'extend_existing': True  # Ensures it won't create a new table if it already exists
    }

    id = Column(BIGINT, primary_key=True, autoincrement=False)
    author = Column(TEXT, ForeignKey(User.accountId), nullable=False)
    body = Column(JSON, nullable=False)
    renderedBody = Column(TEXT, nullable=True)
    updateAuthor = Column(TEXT, ForeignKey(User.accountId), nullable=False)
    created = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    jsdPublic = Column(Boolean, nullable=False)
    issue_id = Column(
        BIGINT,
        ForeignKey(Issue.id, deferrable=True, initially='DEFERRED'),
        nullable=False
    )
    issue_key = Column(
        TEXT,
        ForeignKey(Issue.key, deferrable=True, initially='DEFERRED'),
        nullable=False
    )

    def __repr__(self):
        return (f"<IssueComments(id={self.id}, issue_id={self.issue_id}, "
                f"author='{self.author}', created={self.created})>")


class IssueLinks(Base):
    use_snake_case = True

    id = Column(BIGINT, primary_key=True, autoincrement=False)
    type = Column(JSONB, nullable=False)
    outwardIssue_id = Column(
        BIGINT,
        # ForeignKey(Issue.id, deferrable=True, initially='DEFERRED'),
        nullable=True
    )
    outwardIssue_key = Column(
        TEXT,
        # ForeignKey(Issue.key, deferrable=True, initially='DEFERRED'),
        nullable=True
    )
    inwardIssue_id = Column(
        BIGINT,
        # ForeignKey(Issue.id, deferrable=True, initially='DEFERRED'),
        nullable=True
    )
    inwardIssue_key = Column(
        TEXT,
        # ForeignKey(Issue.key, deferrable=True, initially='DEFERRED'),
        nullable=True
    )
    issue_id = Column(
        BIGINT,
        ForeignKey(Issue.id, deferrable=True, initially='DEFERRED'),
        nullable=False, index=True
    )
    issue_key = Column(
        TEXT,
        ForeignKey(Issue.key, deferrable=True, initially='DEFERRED'),
        nullable=False, index=True
    )


class IssueFields(Base):
    use_snake_case = True

    id = Column(TEXT, primary_key=True)
    key = Column(TEXT)
    name = Column(TEXT)
    untranslatedName = Column(TEXT, nullable=True)
    custom = Column(Boolean)
    orderable = Column(Boolean)
    navigable = Column(Boolean)
    searchable = Column(Boolean)
    schema = Column(JSONB)
    clauseNames = Column(ARRAY(TEXT))
    __table_args__ = (
        {'schema': 'public'}
    )
