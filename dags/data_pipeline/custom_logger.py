# coding=utf-8
import asyncio
import atexit
import io
import logging
import os
import queue
import signal
import sys
import threading
import time
import traceback
from logging.handlers import Timed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ue<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from zoneinfo import ZoneInfo
from rich.logging import <PERSON>Hand<PERSON>
from typing import Dict, Any, Optional, Set
import weakref

os.environ['PYTHONIOENCODING'] = 'utf-8'
def setup_utf8_output():
    """Setup UTF-8 encoding for stdout and stderr on Windows"""
    try:
        # Check if we're on Windows and need to wrap streams
        if sys.platform.startswith('win') and hasattr(sys.stdout, 'detach'):
            # Only wrap if not already wrapped and if encoding is not utf-8
            if not hasattr(sys.stdout, 'buffer') or sys.stdout.encoding.lower() != 'utf-8':
                sys.stdout = io.TextIOWrapper(
                    sys.stdout.detach(),
                    encoding='utf-8',
                    errors='replace'  # This prevents crashes on encoding errors
                )
            if not hasattr(sys.stderr, 'buffer') or sys.stderr.encoding.lower() != 'utf-8':
                sys.stderr = io.TextIOWrapper(
                    sys.stderr.detach(),
                    encoding='utf-8',
                    errors='replace'
                )
    except (AttributeError, OSError):
        # Fallback: streams might already be wrapped or unavailable
        pass

# Call this BEFORE any logging setup
setup_utf8_output()


class UnicodeAwareFilter(logging.Filter):
    """Filter to handle Unicode characters safely"""

    def __init__(self, fallback_encoding='ascii'):
        super().__init__()
        self.fallback_encoding = fallback_encoding

    def filter(self, record):
        """Filter and sanitize log records for Unicode compatibility"""
        try:
            # Try to handle the message
            if hasattr(record, 'msg'):
                record.msg = self._sanitize_unicode(record.msg)

            # Handle args if present
            if hasattr(record, 'args') and record.args:
                record.args = tuple(self._sanitize_unicode(arg) for arg in record.args)

            return True
        except Exception as e:
            # If all else fails, create a safe fallback message
            record.msg = f"[Unicode Error in log message: {str(e)}]"
            record.args = ()
            return True

    def _sanitize_unicode(self, text):
        """Sanitize text to handle Unicode characters safely"""
        if not isinstance(text, str):
            return text

        try:
            # Try to encode/decode to test if the text is safe
            text.encode('utf-8').decode('utf-8')

            # For Windows systems, also test with the console encoding
            if sys.platform.startswith('win'):
                console_encoding = sys.stdout.encoding or 'cp1252'
                try:
                    text.encode(console_encoding)
                except UnicodeEncodeError:
                    # Replace problematic Unicode characters with safe alternatives
                    text = self._replace_unicode_symbols(text)

            return text
        except (UnicodeEncodeError, UnicodeDecodeError):
            # Fallback: encode with error handling
            return text.encode(self.fallback_encoding, errors='replace').decode(self.fallback_encoding)

    def _replace_unicode_symbols(self, text):
        """Replace common Unicode symbols with ASCII alternatives"""
        replacements = {
            '✅': '[SUCCESS]',
            '❌': '[FAILED]',
            '⚠️': '[WARNING]',
            'ℹ️': '[INFO]',
            '🔥': '[HOT]',
            '🚀': '[ROCKET]',
            '📝': '[NOTE]',
            '💡': '[IDEA]',
            '🔍': '[SEARCH]',
            '📊': '[CHART]',
            '🎯': '[TARGET]',
            '⭐': '[STAR]',
            '🔒': '[LOCKED]',
            '🔓': '[UNLOCKED]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '✨': '[SPARKLE]',
            '🎉': '[PARTY]',
            '👍': '[THUMBS_UP]',
            '👎': '[THUMBS_DOWN]',
            '💯': '[100]',
            '🔴': '[RED]',
            '🟢': '[GREEN]',
            '🟡': '[YELLOW]',
            # Add more replacements as needed
        }

        for unicode_char, replacement in replacements.items():
            text = text.replace(unicode_char, replacement)

        return text


class UniversalUnicodeFilter(logging.Filter):
    """
    Universal filter that handles Unicode characters in ALL log messages automatically.
    This filter processes any log message that comes through, regardless of how it was created.
    """

    def __init__(self, fallback_encoding='ascii'):
        super().__init__()
        self.fallback_encoding = fallback_encoding
        self.unicode_replacements = {
            # Success/Failure symbols
            '✅': '[OK]',
            '❌': '[FAIL]',
            '⚠️': '[WARN]',
            'ℹ️': '[INFO]',

            # Common emojis
            '🔥': '[HOT]',
            '🚀': '[ROCKET]',
            '📝': '[NOTE]',
            '💡': '[IDEA]',
            '🔍': '[SEARCH]',
            '📊': '[CHART]',
            '🎯': '[TARGET]',
            '⭐': '[STAR]',
            '🔒': '[LOCKED]',
            '🔓': '[UNLOCKED]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '✨': '[SPARKLE]',
            '🎉': '[PARTY]',
            '👍': '[THUMBS_UP]',
            '👎': '[THUMBS_DOWN]',
            '💯': '[100]',
            '🔴': '[RED]',
            '🟢': '[GREEN]',
            '🟡': '[YELLOW]',
            '🔵': '[BLUE]',
            '🟠': '[ORANGE]',
            '🟣': '[PURPLE]',

            # Arrows and symbols
            '→': '->',
            '←': '<-',
            '↑': '^',
            '↓': 'v',
            '⬆️': '[UP_ARROW]',
            '⬇️': '[DOWN_ARROW]',
            '➡️': '[RIGHT_ARROW]',
            '⬅️': '[LEFT_ARROW]',

            # Mathematical symbols
            '✓': '[CHECK]',
            '✗': '[X]',
            '±': '+/-',
            '×': 'x',
            '÷': '/',
            '∞': '[INFINITY]',

            # Currency and special chars
            '€': 'EUR',
            '£': 'GBP',
            '¥': 'YEN',
            '©': '(c)',
            '®': '(R)',
            '™': '(TM)',
        }

    def filter(self, record):
        """
        Process the log record to handle Unicode characters safely.
        This method is called for EVERY log message that passes through the logger.
        """
        try:
            # Handle the main message
            if hasattr(record, 'msg'):
                record.msg = self._process_unicode_safely(record.msg)

            # Handle message arguments (for formatted strings like logger.info("Hello %s", name))
            if hasattr(record, 'args') and record.args:
                record.args = tuple(self._process_unicode_safely(arg) for arg in record.args)

            return True
        except Exception as e:
            # If something goes wrong, create a safe fallback message
            record.msg = f"[Unicode processing error: {str(e)}] Original level: {record.levelname}"
            record.args = ()
            return True

    def _process_unicode_safely(self, obj):
        """Process any object to handle Unicode characters safely"""
        if not isinstance(obj, str):
            # Convert to string first
            try:
                obj = str(obj)
            except Exception:
                return "[Unprintable object]"

        # First, replace known problematic Unicode characters
        processed_text = self._replace_unicode_characters(obj)

        # Then test if the result can be encoded safely
        try:
            # Test UTF-8 encoding
            processed_text.encode('utf-8').decode('utf-8')

            # On Windows, also test console encoding
            if sys.platform.startswith('win'):
                console_encoding = getattr(sys.stdout, 'encoding', 'cp1252') or 'cp1252'
                try:
                    processed_text.encode(console_encoding)
                except UnicodeEncodeError:
                    # If console encoding fails, fall back to ASCII-safe version
                    processed_text = self._make_ascii_safe(processed_text)

            return processed_text
        except (UnicodeEncodeError, UnicodeDecodeError):
            # Final fallback: make it ASCII-safe
            return self._make_ascii_safe(obj)

    def _replace_unicode_characters(self, text):
        """Replace known Unicode characters with ASCII alternatives"""
        for unicode_char, replacement in self.unicode_replacements.items():
            text = text.replace(unicode_char, replacement)
        return text

    def _make_ascii_safe(self, text):
        """Convert text to ASCII-safe format as a last resort"""
        try:
            return text.encode('ascii', errors='replace').decode('ascii')
        except Exception:
            return "[Text encoding error]"


class SafeUnicodeFormatter(logging.Formatter):
    """Formatter that handles Unicode characters safely"""

    def format(self, record):
        try:
            # Try normal formatting first
            formatted = super().format(record)

            # Test if the formatted string can be encoded safely
            if sys.platform.startswith('win'):
                console_encoding = sys.stdout.encoding or 'cp1252'
                try:
                    formatted.encode(console_encoding)
                except UnicodeEncodeError:
                    # Re-format with sanitized record
                    record.msg = self._sanitize_message(record.msg)
                    if hasattr(record, 'args') and record.args:
                        record.args = tuple(self._sanitize_message(str(arg)) for arg in record.args)
                    formatted = super().format(record)

            return formatted
        except Exception as e:
            # Fallback formatting
            return f"[Formatting Error: {str(e)}] - {record.levelname}: {record.getMessage()}"

    def _sanitize_message(self, msg):
        """Sanitize message for safe encoding"""
        if not isinstance(msg, str):
            return str(msg)

        # Replace problematic Unicode characters
        replacements = {
            '✅': '[OK]',
            '❌': '[FAIL]',
            '⚠️': '[WARN]',
            'ℹ️': '[INFO]',
        }

        for unicode_char, replacement in replacements.items():
            msg = msg.replace(unicode_char, replacement)

        return msg


class MaskAuthorizationFilter(logging.Filter):
    def filter(self, record):
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            try:
                msg_dict = eval(record.msg)
                if isinstance(msg_dict, dict) and 'headers' in msg_dict and 'Authorization' in msg_dict['headers']:
                    msg_dict['headers']['Authorization'] = '***MASKED***'
                    record.msg = str(msg_dict)
            except (SyntaxError, NameError) as e:
                pass  # If it's not a valid dictionary string, do nothing
        return True

class AutoStartQueueListener(QueueListener):

    def __init__(self, queue, *handlers, respect_handler_level=False):
        super().__init__(queue, *handlers, respect_handler_level=respect_handler_level)
        # Start the listener immediately.
        self.start()

    def stop_listener(self):
        self.stop()

class TaskNameFilter(logging.Filter):
    def filter(self, record):
        if hasattr(record, "task_name"):
            # If task_name is already set, don't override
            return True

        try:
            loop = asyncio.get_running_loop()
            task = asyncio.current_task(loop=loop)
            record.task_name = task.get_name() if task else 'NoTask'
        except RuntimeError as e:

            record.task_name = "NoEventLoop"
        return True

class CustomFormatter(logging.Formatter):
    """Custom formatter to include timezone-aware timestamps."""

    TZ_MAP = {"India Standard Time": "Asia/Kolkata"}

    def formatTime(self, record, datefmt=None):
        local_time = datetime.now().astimezone()
        timezone_str = local_time.strftime('%Z')
        local_timezone = ZoneInfo(self.TZ_MAP.get(timezone_str, timezone_str))
        dt = datetime.fromtimestamp(record.created, local_timezone)
        return dt.strftime(datefmt or "%Y-%m-%d %H:%M:%S %Z")


class CustomConsoleHandler(RichHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add the universal Unicode filter.
        # This is KEY for making existing calls work
        self.addFilter(UniversalUnicodeFilter())

    def emit(self, record):
        # Only emit logs if the level is WARNING or higher
        if record.levelno >= logging.WARNING:
            try:
                formatted_record = self.format(record)
                self.console.print(formatted_record)
            except Exception as e:
                self.handleError(record)
                raise e


class SafeTimedRotatingFileHandler(TimedRotatingFileHandler):
    """Custom file handler to handle file permission errors during log rotation."""

    def __init__(self, *args, **kwargs):
        # Ensure UTF-8 encoding for file handler
        kwargs.setdefault('encoding', 'utf-8')
        super().__init__(*args, **kwargs)
        # Add the universal Unicode filter
        # This is KEY for making your existing calls work
        self.addFilter(UniversalUnicodeFilter())

    def doRollover(self):
        retries = 5
        while retries > 0:
            try:
                super().doRollover()
                break
            except PermissionError:
                retries -= 1
                if retries == 0:
                    raise


class DebuggingMonitor:
    """
    Advanced debugging monitor for tracking async operations, queue states, and potential deadlocks.
    Can be enabled when the code gets stuck during rate limiting or other issues.
    """

    def __init__(self, enabled: bool = False, log_interval: int = 30):
        self.enabled = enabled
        self.log_interval = log_interval
        self.logger = logging.getLogger("debugging_monitor")
        self.start_time = time.time()
        self.last_activity = {}
        self.queue_states = {}
        self.task_states = {}
        self.http_request_count = 0
        self.rate_limit_count = 0
        self.db_operation_count = 0
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        self._active_tasks = weakref.WeakSet()
        self._active_queues = weakref.WeakSet()

        if self.enabled:
            self.start_monitoring()

    def start_monitoring(self):
        """Start the monitoring thread"""
        if self.monitoring_thread is None or not self.monitoring_thread.is_alive():
            self.stop_monitoring.clear()
            self.monitoring_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitoring_thread.start()
            self.logger.info("Debugging monitor started")

    def stop(self):
        """Stop the monitoring thread"""
        self.stop_monitoring.set()
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
        self.logger.info("Debugging monitor stopped")

    def _monitor_loop(self):
        """Main monitoring loop that runs in a separate thread"""
        while not self.stop_monitoring.wait(self.log_interval):
            try:
                self._log_system_state()
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")

    def _log_system_state(self):
        """Log comprehensive system state"""
        current_time = time.time()
        uptime = current_time - self.start_time

        # Get asyncio event loop info
        try:
            loop = asyncio.get_running_loop()
            loop_info = {
                "running": loop.is_running(),
                "closed": loop.is_closed(),
                "dataframe_utils": loop.get_debug(),
                "task_count": len(asyncio.all_tasks(loop))
            }
        except RuntimeError:
            loop_info = {"error": "No running event loop"}

        # Log comprehensive state
        self.logger.warning(f"""
=== DEBUGGING MONITOR REPORT (Uptime: {uptime:.1f}s) ===
Event Loop: {loop_info}
HTTP Requests: {self.http_request_count}
Rate Limits Hit: {self.rate_limit_count}
DB Operations: {self.db_operation_count}
Active Tasks: {len(self._active_tasks)}
Active Queues: {len(self._active_queues)}
Queue States: {self._get_queue_summary()}
Task States: {self._get_task_summary()}
Memory Usage: {self._get_memory_info()}
Thread Count: {threading.active_count()}
=== END REPORT ===
        """)

    def _get_queue_summary(self) -> Dict[str, Any]:
        """Get summary of queue states"""
        summary = {}
        for queue_name, state in self.queue_states.items():
            summary[queue_name] = {
                "size": state.get("size", "unknown"),
                "last_activity": time.time() - state.get("last_activity", time.time()),
                "total_items": state.get("total_items", 0)
            }
        return summary

    def _get_task_summary(self) -> Dict[str, Any]:
        """Get summary of task states"""
        summary = {}
        try:
            loop = asyncio.get_running_loop()
            tasks = asyncio.all_tasks(loop)

            for task in tasks:
                task_name = task.get_name()
                summary[task_name] = {
                    "done": task.done(),
                    "cancelled": task.cancelled(),
                    "running_time": time.time() - self.task_states.get(task_name, {}).get("start_time", time.time())
                }
        except RuntimeError:
            summary = {"error": "No running event loop"}

        return summary

    def _get_memory_info(self) -> Dict[str, Any]:
        """Get memory usage information"""
        try:
            import psutil
            process = psutil.Process()
            return {
                "rss_mb": process.memory_info().rss / 1024 / 1024,
                "vms_mb": process.memory_info().vms / 1024 / 1024,
                "percent": process.memory_percent()
            }
        except ImportError:
            return {"error": "psutil not available"}

    def log_http_request(self, method: str, url: str, status: Optional[int] = None):
        """Log HTTP request activity"""
        if not self.enabled:
            return

        self.http_request_count += 1
        if status == 429:
            self.rate_limit_count += 1
            self.logger.warning(f"RATE LIMIT HIT: {method} {url} - Total rate limits: {self.rate_limit_count}")

        self.last_activity["http"] = time.time()

    def log_queue_activity(self, queue_name: str, operation: str, queue_size: int):
        """Log queue activity"""
        if not self.enabled:
            return

        if queue_name not in self.queue_states:
            self.queue_states[queue_name] = {"total_items": 0}

        self.queue_states[queue_name].update({
            "size": queue_size,
            "last_activity": time.time(),
            "last_operation": operation
        })

        if operation in ["put", "get"]:
            self.queue_states[queue_name]["total_items"] += 1

        # Log if queue seems stuck
        if queue_size > 100:
            self.logger.warning(f"Large queue detected: {queue_name} has {queue_size} items")

    def log_db_operation(self, operation: str, duration: Optional[float] = None):
        """Log database operation"""
        if not self.enabled:
            return

        self.db_operation_count += 1
        self.last_activity["db"] = time.time()

        if duration and duration > 5.0:  # Log slow operations
            self.logger.warning(f"Slow DB operation: {operation} took {duration:.2f}s")

    def log_task_start(self, task_name: str):
        """Log task start"""
        if not self.enabled:
            return

        self.task_states[task_name] = {
            "start_time": time.time(),
            "status": "running"
        }

    def log_task_end(self, task_name: str, success: bool = True):
        """Log task completion"""
        if not self.enabled:
            return

        if task_name in self.task_states:
            duration = time.time() - self.task_states[task_name]["start_time"]
            self.task_states[task_name].update({
                "status": "completed" if success else "failed",
                "duration": duration
            })

    def register_queue(self, queue, name: str):
        """Register a queue for monitoring"""
        if not self.enabled:
            return

        self._active_queues.add(queue)
        self.queue_states[name] = {
            "size": getattr(queue, 'qsize', lambda: 0)(),
            "last_activity": time.time(),
            "total_items": 0
        }

    def register_task(self, task, name: str):
        """Register a task for monitoring"""
        if not self.enabled:
            return

        self._active_tasks.add(task)
        self.log_task_start(name)

    def dump_stack_traces(self):
        """Dump stack traces of all threads - useful when stuck"""
        if not self.enabled:
            return

        relevant_threads = []

        for thread_id, frame in sys._current_frames().items():
            thread = threading.current_thread()
            # Skip system/logging threads
            if any(skip_pattern in thread.name.lower() for skip_pattern in
                   ['logging', 'queue', '_monitor', 'dummy']):
                continue
            relevant_threads.append((thread_id, thread, frame))
        if relevant_threads:
            self.logger.error("=== STACK TRACE DUMP ===")
            for thread_id, thread, frame in relevant_threads:
                # Skip logging threads
                self.logger.error(f"Thread {thread_id}:")
                self.logger.error(''.join(traceback.format_stack(frame)))
            self.logger.error("=== END STACK TRACE DUMP ===")

    def force_log_state(self):
        """Force immediate state logging"""
        if self.enabled:
            self._log_system_state()


# Global debugging monitor instance
debug_monitor = DebuggingMonitor(enabled=os.getenv("ENABLE_DEBUG_MONITOR", "false").lower() == "true")


class DebugSignalHandler:
    """
    Signal handler for debugging on Unix systems.
    Sends SIGUSR1 to dump stack traces and system state.
    """

    def __init__(self, monitor: DebuggingMonitor):
        self.monitor = monitor
        self.setup_signal_handlers()

    def setup_signal_handlers(self):
        """Setup signal handlers for debugging"""
        if sys.platform != "win32":  # Unix systems only
            try:
                signal.signal(signal.SIGUSR1, self._handle_debug_signal)
                signal.signal(signal.SIGUSR2, self._handle_state_dump)
                self.monitor.logger.info("Debug signal handlers installed (SIGUSR1: stack traces, SIGUSR2: state dump)")
            except (OSError, ValueError) as e:
                self.monitor.logger.warning(f"Could not install signal handlers: {e}")

    def _handle_debug_signal(self, signum, frame):
        """Handle dataframe_utils signal - dump stack traces"""
        self.monitor.logger.warning("Debug signal received - dumping stack traces")
        self.monitor.dump_stack_traces()

    def _handle_state_dump(self, signum, frame):
        """Handle state dump signal"""
        self.monitor.logger.warning("State dump signal received")
        self.monitor.force_log_state()


# Initialize signal handler
if debug_monitor.enabled:
    signal_handler = DebugSignalHandler(debug_monitor)


# Helper function for safe Unicode logging
def safe_log_message(success: bool, func_name: str, use_unicode: bool = True) -> str:
    """
    Create a safe log message that handles Unicode characters properly.

    Args:
        success: Whether the operation was successful
        func_name: Name of the function being logged
        use_unicode: Whether to use Unicode symbols (will fallback to ASCII if encoding fails)

    Returns:
        A safely formatted log message
    """
    if use_unicode:
        try:
            # Test if Unicode can be encoded safely
            test_msg = "✅ test" if success else "❌ test"
            if sys.platform.startswith('win'):
                console_encoding = sys.stdout.encoding or 'cp1252'
                test_msg.encode(console_encoding)

            # If we get here, Unicode is safe to use
            symbol = "✅" if success else "❌"
            status = "completed successfully" if success else "failed"
            return f"{symbol} {func_name} test {status}"
        except (UnicodeEncodeError, AttributeError):
            # Fallback to ASCII
            pass

    # ASCII fallback
    symbol = "[SUCCESS]" if success else "[FAILED]"
    status = "completed successfully" if success else "failed"
    return f"{symbol} {func_name} test {status}"


# Example usage function
def log_test_result(logger, func_name: str, success: bool):
    """
    Safely log test results with Unicode handling.

    Args:
        logger: The logger instance
        func_name: Name of the function being tested
        success: Whether the test was successful
    """
    message = safe_log_message(success, func_name)

    if success:
        logger.info(message)
    else:
        logger.error(message)


class CustomLogger:
    """Custom logger with RichHandler for console and file-based logging."""

    def __init__(self, name, log_file, use_formatter=True):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        self.log_queue = queue.Queue(-1)  # Unlimited size

        # Setup handlers
        console_handler = self._get_console_handler(use_formatter)
        file_handler = self._get_file_handler(log_file, use_formatter)

        # Queue handler for thread-safe logging
        queue_handler = QueueHandler(self.log_queue)

        # IMPORTANT: Add the Unicode filter to the queue handler as well
        # This ensures ALL messages are processed before they go to the queue
        queue_handler.addFilter(UniversalUnicodeFilter())

        self.logger.addHandler(queue_handler)

        # Listener to process logs from the queue
        self.listener = AutoStartQueueListener(
            self.log_queue,
            console_handler,
            file_handler,
            respect_handler_level=True
        )

        # Stop listener on application exit
        atexit.register(self.listener.stop)
        self.logger.propagate = False

    def _get_console_handler(self, use_formatter):
        """Configure RichHandler for console logs."""
        console_handler = CustomConsoleHandler(rich_tracebacks=True, markup=True)
        console_handler.setLevel(logging.WARNING)  # Only show warnings and above on console

        if use_formatter:
            formatter = CustomFormatter(
                "%(asctime)s — %(name)s — %(levelname)s — %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S %Z"
            )
            console_handler.setFormatter(formatter)
        return console_handler

    def _get_file_handler(self, log_file, use_formatter):
        """Configure TimedRotatingFileHandler for file-based logs."""
        file_handler = SafeTimedRotatingFileHandler(
            log_file,
            when="midnight",
            backupCount=3,
            delay=True,
            encoding='utf-8'  # Explicitly set UTF-8 encoding
        )
        file_handler.setLevel(logging.DEBUG)

        if use_formatter:
            formatter = CustomFormatter(
                "%(asctime)s.%(msecs)03d %(levelname)-8s %(funcName)-31s %(lineno)-4d | %(message)s",
                datefmt="%Y-%m-%dT%H:%M:%S"
            )
            file_handler.setFormatter(formatter)
        return file_handler

    def get_logger(self):
        """Return the configured logger."""
        return self.logger


# Usage example - your existing code will now work without changes!
if __name__ == "__main__":
    # Setup your logger as usual
    log_file_name = os.getenv("AIRFLOW_HOME", "/tmp") + "/main.log"
    custom_logger = CustomLogger(__name__, log_file_name, use_formatter=True)
    my_logger = custom_logger.get_logger()

    # Your existing code will now work without modification!
    func_name = "test_function"
    success = True

    # These calls will now automatically handle Unicode characters safely
    if success:
        my_logger.info(f"✅ {func_name} test completed successfully")
    else:
        my_logger.error(f"❌ {func_name} test failed")

    # Test with failure case
    success = False
    if success:
        my_logger.info(f"✅ {func_name} test completed successfully")
    else:
        my_logger.error(f"❌ {func_name} test failed")
