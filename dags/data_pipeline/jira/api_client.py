import asyncio
import random
from datetime import datetime, timezone
from logging import Logger
from typing import Union, Dict, Any

import aiohttp
from dateutil import parser
from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import LoggerContainer, GlobalCircuitBreaker, ApplicationContainer, CircuitState, \
    JiraEntryDetailsContainer
from dags.data_pipeline.debug.debug_utils import rate_limit_tracker


MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 5000.0  # in milliseconds
MAX_RETRY_DELAY = 10000  # in milliseconds
JITTER_MULTIPLIER_RANGE = (0.5, 1.5)


@inject
async def fetch_with_retries(
        session: aiohttp.ClientSession,
        method: str,
        url: str,
        *,
        params: Union[Dict[str, Any], None] = None,
        json_payload: Union[Dict[str, Any], None] = None,
        retries: int = MAX_RETRIES,
        my_logger: Logger = Provide[LoggerContainer.logger],
        global_circuit_breaker: GlobalCircuitBreaker = Provide[ApplicationContainer.circuit_breaker],
) -> Dict[str, Any]:
    """
    Perform an HTTP request with retry logic.

    :param session: aiohttp ClientSession.
    :param method: HTTP method (e.g., 'GET', 'POST', 'PUT').
    :param url: API endpoint URL.
    :param params: HTTP request parameters for GET.
    :param json_payload: JSON body for POST or PUT.
    :param retries: Maximum number of retries.
    :param my_logger: Logger instance.
    :param global_circuit_breaker: Circuit breaker instance.
    :return: Dictionary with keys:
        - 'success': bool - True if request succeeded, False otherwise
        - 'result': Any - Response data if success=True, None if success=False
        - 'exception': str - Error message if success=False, not present if success=True
    """
    asyncio.current_task().set_name("fetch_with_retries")
    retry_count = 0
    retry_delay = INITIAL_RETRY_DELAY

    last_exception = None
    my_logger.info(f"fetch_with_retries: {url}, type: {type(global_circuit_breaker)}")

    while retry_count <= retries:
        # Check circuit breaker before making request
        if not await global_circuit_breaker.can_execute():
            my_logger.warning("Circuit breaker is OPEN or rate limited. Waiting for recovery...")
            await global_circuit_breaker.wait_for_recovery(timeout=300.0)

            # Check again after waiting
            if not await global_circuit_breaker.can_execute():
                my_logger.error("Circuit breaker still OPEN after waiting. Aborting request.")
                my_logger.error(f"can't process request: {url}")
                return {"success": False, "exception": "Circuit breaker OPEN", "result": None}

        await global_circuit_breaker.enter_request()
        request_successful = False
        need_retry = False

        try:
            # async with debug_http_request(session, method, url):
            async with session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_payload,
            ) as response:
                my_logger.debug(f"status: {response.status}, url: {response.url}")
                if response.status in (200, 201, 204):
                    await global_circuit_breaker.record_success()
                    request_successful = True

                    # Monitor rate limit headers for proactive management
                    rate_limit_remaining = response.headers.get("X-RateLimit-Remaining")
                    rate_limit_limit = response.headers.get("X-RateLimit-Limit")
                    near_limit = response.headers.get("X-RateLimit-NearLimit") == "true"

                    if rate_limit_remaining and rate_limit_limit:
                        remaining_pct = (int(rate_limit_remaining) / int(rate_limit_limit)) * 100
                        my_logger.debug(f"Rate limit status: {rate_limit_remaining}/{rate_limit_limit} ({remaining_pct:.1f}% remaining)")

                    # Success handling
                    if response.status == 204:
                        my_logger.info("Request successful, but no content to return.")
                        return {"success": True, "result": None}
                    else:
                        if near_limit:
                            # Coordinate global rate limit warning across all threads
                            warning_duration = max(2.0, 2 ** retry_count)  # At least 2 seconds
                            my_logger.warning(f"Warning: Less than 20% of the rate limit budget remains.")
                            my_logger.warning(f"Activating global rate limit warning for {warning_duration} seconds.")
                            await global_circuit_breaker.record_rate_limit_warning(warning_duration)
                            # Wait for the global warning to clear
                            await global_circuit_breaker.wait_for_recovery()
                        return {"success": True, "result": await response.json()}

                elif response.status == 429 or "Retry-After" in response.headers:
                    # Retry logic - record rate limit event
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                    rate_limit_tracker.record_rate_limit(url, retry_delay)
                    await global_circuit_breaker.record_rate_limit(retry_delay)
                    need_retry = True
                    my_logger.warning(
                        f"Rate limited or server unavailable. Retrying in {retry_delay / 1000:.2f} seconds. "
                        f"Consecutive rate limits: {rate_limit_tracker.consecutive_rate_limits}"
                    )
                    # Wait for the circuit breaker to coordinate the delay
                    await global_circuit_breaker.wait_for_recovery()
                elif response.status == 503:
                    # Service unavailable - could be a service issue
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                    await global_circuit_breaker.record_failure()
                    need_retry = True
                    my_logger.warning(f"Service unavailable (503). Will retry after backoff.")
                else:
                    # Other errors - actual failures
                    await global_circuit_breaker.record_failure()
                    my_logger.error(f"Request failed with status {response.status}. url = {response.url}")
                    return {"success": False, "exception": f"HTTP {response.status}"}
                response.raise_for_status()

        except aiohttp.ClientResponseError as e:
            my_logger.info(f"HTTP error {e.status}: {e.message}")
            last_exception = f"HTTP error {e.status}: {e.message}"
            if e.status not in (429, 503):
                await global_circuit_breaker.record_failure()
                need_retry = True  # Allow retry for retriable HTTP errors
            else:
                need_retry = True

        except aiohttp.client_exceptions.ConnectionTimeoutError as e:
            my_logger.error(f"Request error: {e}. Retrying {retry_count}/{retries} times.")
            last_exception = f"Connection timeout: {str(e)}"
            await global_circuit_breaker.record_failure()
            need_retry = True

        except aiohttp.client_exceptions.ClientConnectionResetError as e:
            my_logger.error(f"Connection reset: {e}. Retrying {retry_count}/{retries} times.")
            last_exception = f"Connection reset: {str(e)}"
            await global_circuit_breaker.record_failure()
            need_retry = True

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            my_logger.error(f"Request error: {e}. Retrying {retry_count}/{retries} times.")
            last_exception = str(e)  # Store the exception message
            await global_circuit_breaker.record_failure()
            need_retry = True  # Set flag to indicate retry is needed

        except Exception as e:
            my_logger.error(f"Unexpected error: {e}")
            # For unexpected errors, don't retry and return failure immediately
            return {"success": False, "exception": f"Unexpected error: {str(e)}"}
        finally:
            await global_circuit_breaker.exit_request()

        # If request was successful, return immediately (no retry needed)
        if request_successful:
            break

        # Handle retry logic only when needed
        if need_retry and retry_count < retries:
            retry_count += 1

            # Only add jitter if circuit breaker isn't coordinating delays
            # and we're not dealing with rate limits (which have their own coordination)
            if (global_circuit_breaker.state == CircuitState.CLOSED and
                    not (retry_count == 1)):  # Skip jitter on first retry to be more responsive

                jitter = random.uniform(0.8, 1.2)  # JITTER_MULTIPLIER_RANGE
                jitter_delay = round(retry_delay * jitter / 1000, 2)
                my_logger.warning(f"Adding jitter of {jitter:.2f}. Sleeping for {jitter_delay} seconds.")
                await asyncio.sleep(jitter_delay)

        elif not need_retry or retry_count >= retries:
            # Either we don't need to retry or we've exhausted retries
            break

        # Handle retry delay
        if retry_count  > retries:
            my_logger.error(f"Exceeded maximum retries ({retries}).")
            break

        jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        await asyncio.sleep((retry_delay + jitter) / 1000)  # Add jitter

    # If we reach here, all retries have been exhausted
    # Return failure response with the last exception encountered
    if last_exception:
        my_logger.error(f"All retries exhausted. Last error: {last_exception}")
        # Trigger graceful shutdown if not a rate limit error
        if not (isinstance(last_exception, str) and ("rate limit" in last_exception.lower() or "429" in last_exception)):
            await global_circuit_breaker.trigger_graceful_shutdown(
                reason=f"All retries exhausted in fetch_with_retries. Last error: {last_exception}"
            )
        return {"success": False, "exception": last_exception}
    else:
        my_logger.error("All retries exhausted with no specific error recorded.")
        await global_circuit_breaker.trigger_graceful_shutdown(
            reason="All retries exhausted in fetch_with_retries with no specific error recorded."
        )
        return {"success": False, "exception": "Maximum retries exceeded"}


async def fetch_with_retries_post(
        session: aiohttp.ClientSession,
        url: str,
        json_payload: Dict[str, Any]
) -> Dict[str, Any]:
    """
    POST wrapper for fetch_with_retries.

    :return: Dictionary with keys 'success', 'result', and 'exception' (if failed)
    """
    asyncio.current_task().set_name("fetch_with_retries_post")
    return await fetch_with_retries(
        session=session,
        method="POST",
        url=url,
        json_payload=json_payload,
    )


async def fetch_with_retries_get(
        session: aiohttp.ClientSession,
        url: str,
        params: Union[Dict[str, Any], None] = None,
        # my_logger: Logger = Provide[LoggerContainer.logger],
) -> Dict[str, Any]:
    """
    GET wrapper for fetch_with_retries.

    :return: Dictionary with keys 'success', 'result', and 'exception' (if failed)
    """
    asyncio.current_task().set_name("fetch_with_retries_get")
    return await fetch_with_retries(
        session=session,
        method="GET",
        url=url,
        params=params
    )


async def calculate_retry_delay(
        response: aiohttp.ClientResponse, last_retry_delay: float,
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> float:
    """
    Calculate the delay for the next retry attempt.

    :param response: The HTTP response object.
    :param last_retry_delay: The last retry delay in milliseconds.
    :param my_logger: logger instance Injected from container
    :return: The new retry delay in milliseconds.
    """
    retry_delay = -1

    # Check for the Retry-After header
    if "Retry-After" in response.headers:
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Check for the X-RateLimit-Reset header
    elif "X-RateLimit-Reset" in response.headers:
        reset_time = parser.parse(response.headers["X-RateLimit-Reset"])
        current_time = datetime.now(timezone.utc)
        wait_time = (reset_time - current_time).total_seconds() * 1000  # Convert to milliseconds
        retry_delay = max(wait_time, last_retry_delay)

    # If no headers are present but 429 status is received, double the last delay
    elif response.status == 429:
        retry_delay = min(2 * last_retry_delay, MAX_RETRY_DELAY)

    elif response.status in (503, 500) and "Retry-After" in response.headers:
        # Handle transient 5XX errors with Retry-After header
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Apply jitter
    if retry_delay > 0:
        jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        retry_delay += int(retry_delay * jitter)

    # Log and handle X-RateLimit-NearLimit
    if response.headers.get("X-RateLimit-NearLimit") == "true":
        retry_delay = max(retry_delay, 5000)  # Example delay of 5 seconds
        my_logger.warning(f"Less than 20% of the budget remains. sleeping for {retry_delay / 1000} seconds.")
        # Introduce a delay when near limit is reached
        await asyncio.sleep(retry_delay / 1000)

    return retry_delay


logger_container = LoggerContainer()
application_container = ApplicationContainer()
logger_container.wire(modules=["dags.data_pipeline.jira.api_client"])

application_container.wire(modules=["dags.data_pipeline.jira.api_client"])
jira_container = JiraEntryDetailsContainer()
jira_container.wire(["dags.data_pipeline.jira.api_client"])