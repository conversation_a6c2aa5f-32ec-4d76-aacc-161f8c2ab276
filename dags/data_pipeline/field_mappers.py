# coding=utf-8
"""
Field mapping and transformation utilities for JIRA data processing.

This module provides configuration-driven field mapping and transformation
capabilities to replace hardcoded field mappings in the consume functions.
"""

import yaml
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from dependency_injector.wiring import inject, Provide

try:
    from containers import LoggerContainer
except ModuleNotFoundError:
    from .containers import LoggerContainer


@dataclass
class FieldMapping:
    """Represents a field mapping configuration."""
    id: str
    datatype: str
    custom: bool
    name: str
    mapping: Optional[Dict[str, str]] = None
    target_type: Optional[str] = None


class FieldMapper:
    """Handles field mapping and transformation based on YAML configuration."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the FieldMapper with configuration.
        
        Args:
            config_path: Path to the YAML configuration file
        """
        if config_path is None:
            config_path = Path(__file__).parent / "enhanced_issue_fields.yaml"
        
        self.config_path = Path(config_path)
        self._field_mappings: Dict[str, FieldMapping] = {}
        self._column_rename_map: Dict[str, str] = {}
        self._type_mapping: Dict[str, str] = {}
        self._load_configuration()
    
    def _load_configuration(self):
        """Load field mappings from YAML configuration."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            for field_config in config.get('fields', []):
                field_mapping = FieldMapping(
                    id=field_config['id'],
                    datatype=field_config['datatype'],
                    custom=field_config['custom'],
                    name=field_config['name'],
                    mapping=field_config.get('mapping'),
                    target_type=field_config.get('target_type')
                )
                
                self._field_mappings[field_mapping.id] = field_mapping
                
                # Build column rename map
                if field_mapping.mapping:
                    self._column_rename_map.update(field_mapping.mapping)
                
                # Build type mapping
                if field_mapping.target_type:
                    if field_mapping.mapping:
                        for target_col in field_mapping.mapping.values():
                            self._type_mapping[target_col] = field_mapping.target_type
                    else:
                        self._type_mapping[field_mapping.id] = field_mapping.target_type
                        
        except Exception as e:
            raise RuntimeError(f"Failed to load field configuration from {self.config_path}: {e}")
    
    def get_column_rename_map(self) -> Dict[str, str]:
        """Get the complete column rename mapping."""
        return self._column_rename_map.copy()
    
    def get_type_mapping(self) -> Dict[str, str]:
        """Get the complete type mapping."""
        return self._type_mapping.copy()
    
    def get_field_mapping(self, field_id: str) -> Optional[FieldMapping]:
        """Get field mapping for a specific field ID."""
        return self._field_mappings.get(field_id)
    
    def get_drop_column_prefixes(self) -> List[str]:
        """Get list of column prefixes that should be dropped."""
        return [
            'parent.',
            'customfield_10001.',
            'worklog.',
            'comment.comments',
            'description.', 'assignee.', 'customfield_10006.',
            'customfield_10049.', 'reporter.',
            "customfield_10056.",
            "customfield_10071.",
            "customfield_10078.",
            'customfield_10146.',
            "customfield_10179.", "issuetype.", "priority.",
            "resolution.", 'status.',
            'customfield_10092.'
        ]
    
    def get_drop_column_exceptions(self) -> set:
        """Get set of columns that should NOT be dropped despite matching prefixes."""
        return {
            'parent.id', 'parent.key', 'assignee.accountId', 'customfield_10006.value',
            'customfield_10001.name',
            'customfield_10049.value', 'reporter.accountId',
            "customfield_10056.value",
            "customfield_10071.value",
            "customfield_10078.value",
            'customfield_10146.value',
            "customfield_10179.value", 'issuetype.name', 'issuetype.subtask', 'issuetype.hierarchyLevel',
            'priority.name',
            'resolution.name', 'status.name', 'status.statusCategory.name',
            'customfield_10092.value'
        }
    
    def apply_field_mappings(
        self,
        df: pd.DataFrame,
        my_logger=None
    ) -> pd.DataFrame:
        """
        Apply field mappings to a DataFrame.
        
        Args:
            df: Input DataFrame
            my_logger: Logger instance
            
        Returns:
            Transformed DataFrame
        """
        df = df.copy()
        
        # Apply column renaming
        rename_map = self.get_column_rename_map()
        existing_rename_map = {k: v for k, v in rename_map.items() if k in df.columns}
        
        if existing_rename_map:
            df.rename(columns=existing_rename_map, inplace=True)
            if my_logger:
                my_logger.debug(f"Renamed {len(existing_rename_map)} columns using field mapping")
        
        return df
    
    def get_columns_by_type(self, target_type: str) -> List[str]:
        """Get list of target column names for a specific type."""
        columns = []
        for field_id, field_mapping in self._field_mappings.items():
            if field_mapping.target_type == target_type:
                if field_mapping.mapping:
                    columns.extend(field_mapping.mapping.values())
                else:
                    columns.append(field_id)
        return columns


class DataTypeMapper:
    """Handles data type mapping and conversion."""
    
    TYPE_MAPPING = {
        'int64': pd.Int64Dtype(),
        'float64': pd.Float64Dtype(),
        'string': 'string',
        'datetime': 'datetime',
        'date': 'date',
        'json': 'object',
        'array_string': 'object'
    }
    
    @classmethod
    def get_pandas_dtype(cls, type_name: str, my_logger=None):
        """Convert type name to pandas dtype."""
        dtype = cls.TYPE_MAPPING.get(type_name)
        if dtype is None:
            if my_logger:
                my_logger.warning(f"Unknown type mapping: {type_name}, defaulting to object")
            return 'object'
        return dtype
    
    @classmethod
    def safe_type_conversion(
        cls,
        df: pd.DataFrame,
        column: str,
        target_type: str,
        my_logger=None
    ) -> pd.DataFrame:
        """
        Safely convert a column to target type with error handling.
        
        Args:
            df: DataFrame to modify
            column: Column name to convert
            target_type: Target type name
            my_logger: Logger instance
            
        Returns:
            Modified DataFrame
        """
        if column not in df.columns:
            return df
        
        try:
            if target_type == 'datetime':
                df[column] = pd.to_datetime(df[column], errors='coerce')
            elif target_type == 'date':
                df[column] = pd.to_datetime(df[column], errors='coerce').dt.date
            elif target_type in ['int64', 'float64']:
                # Clean the data first
                if df[column].dtype == 'object':
                    df[column] = df[column].astype(str).str.strip()
                    df[column] = df[column].replace(['', 'nan', 'null', 'None'], pd.NA)
                    df[column] = df[column].replace(r'\.0$', '', regex=True)
                
                if target_type == 'int64':
                    df[column] = pd.to_numeric(df[column], errors='coerce')
                    df[column] = df[column].astype(pd.Int64Dtype())
                else:
                    df[column] = pd.to_numeric(df[column], errors='coerce')
                    df[column] = df[column].astype(pd.Float64Dtype())
            else:
                pandas_dtype = cls.get_pandas_dtype(target_type, my_logger)
                df[column] = df[column].astype(pandas_dtype)

            if my_logger:
                my_logger.debug(f"Successfully converted column '{column}' to {target_type}")

        except Exception as e:
            if my_logger:
                my_logger.error(f"Failed to convert column '{column}' to {target_type}: {e}")
            # Keep original data type on failure
            
        return df


# Global instance for easy access
field_mapper = FieldMapper()
data_type_mapper = DataTypeMapper()


def get_field_mapper() -> FieldMapper:
    """Get the global field mapper instance."""
    return field_mapper


def get_data_type_mapper() -> DataTypeMapper:
    """Get the global data type mapper instance."""
    return data_type_mapper
