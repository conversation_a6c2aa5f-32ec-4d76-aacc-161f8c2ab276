# Read the input csv file
# populate team name to create the script for
# add the insert statement
# Add resource name as comment
# generate script

with open("e:/vishal/input.txt") as fp:
    line = fp.readlines()

team_name = input("Enter team name: ")

textfile = open("e:/vishal/out.sql", "w")

for i in line:
    accountid, name, designation = i.split('\t')
    designation = designation.strip("\n")
    textfile.write(f'-- {name}\n')
    textfile.write(f'INSERT INTO teams(team_name, "accountId", designation) VALUES (\'{team_name}\', \'{accountid}\', \'{designation}\'); ')
    textfile.write('\n')

print('Completed!!!')
