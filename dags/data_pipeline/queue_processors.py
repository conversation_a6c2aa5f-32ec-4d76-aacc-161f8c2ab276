# coding=utf-8
"""
Queue processing utilities for JIRA data pipeline.

This module contains the refactored consume functions with improved
error handling, field mapping, and data type conversion.
"""
import asyncio
import sys
import traceback
from logging import Logger

import pandas as pd
import markdownify
from asyncio import Queue
from typing import Any, Optional

from dependency_injector.wiring import inject, Provide
from sqlalchemy.ext.asyncio import AsyncSession


try:
    from containers import Logger<PERSON>ontainer
    from field_mappers import get_field_mapper
    from data_type_handlers import create_robust_type_handler
    from dbmodels.issue import Issue, IssueComments, IssueLinks
    from dbmodels.changelog import ChangelogJSON
    from dbmodels.worklog import WorkLog
    from dbmodels.initiativeattribute import InitiativeAttribute
    from dags.data_pipeline.debug.debug_utils import debug_queue_operation
    # from utility_code import (
    #     add_issue_key_issue_id, fetch_changelog, handle_exception,
    #     lock, commit_transaction
    # )
except ModuleNotFoundError:
    from .containers import LoggerContainer
    from .field_mappers import get_field_mapper
    from .data_type_handlers import create_robust_type_handler
    from .dbmodels.issue import Issue, IssueComments, IssueLinks
    from .dbmodels.changelog import ChangelogJSON
    from .dbmodels.worklog import WorkLog
    from .dbmodels.initiativeattribute import InitiativeAttribute
    from dags.data_pipeline.debug.debug_utils import debug_queue_operation
    # from .utility_code import (
    #     add_issue_key_issue_id, fetch_changelog, handle_exception,
    #     lock, commit_transaction
    # )

lock = asyncio.Lock()

@inject
def handle_exception(e, my_logger: Logger = Provide[LoggerContainer.logger]):
    exc_type, exc_value, exc_tb = sys.exc_info()
    line_num = exc_tb.tb_lineno
    tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
    my_logger.exception(
        f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
        exc_info=True
    )
    raise e

class BaseQueueProcessor:
    """Base class for queue processors with common functionality."""
    
    def __init__(self):
        self.field_mapper = get_field_mapper()
        self.type_handler = create_robust_type_handler()
    
    async def process_queue_item(
        self,
        item: Any,
        queue_upsert_issue: Queue,
        my_logger=None
    ) -> bool:
        """
        Process a single queue item. To be implemented by subclasses.
        
        Args:
            item: Queue item to process
            queue_upsert_issue: Queue for upsert operations
            my_logger: Logger instance
            
        Returns:
            True if processing should continue, False to stop
        """
        raise NotImplementedError("Subclasses must implement process_queue_item")
    
    async def run_processor(
        self,
        queue_id: int,
        name: str,
        input_queue: Queue,
        queue_upsert_issue: Queue,
        my_logger=None
    ):
        """
        Main processor loop with error handling.
        
        Args:
            queue_id: Processor ID
            name: Processor name
            input_queue: Input queue to process
            queue_upsert_issue: Output queue for upsert operations
            my_logger: Logger instance
        """
        try:
            loop_count = 0
            
            while True:
                loop_count += 1
                
                try:
                    my_logger.debug(f"queue_{name} q_type: {type(input_queue)}")
                    async with debug_queue_operation(input_queue, "get", f"queue_{name}") as item:
                        should_continue = await self.process_queue_item(
                            item, queue_upsert_issue, my_logger
                        )
                        
                        if not should_continue:
                            await queue_upsert_issue.put(None)
                            break
                            
                finally:
                    async with debug_queue_operation(input_queue, "task_done", f"queue_{name}"):
                        pass
                        
        except Exception as e:
            global commit_transaction
            async with lock:
                commit_transaction = False
            handle_exception(e)
        finally:
            if my_logger:
                my_logger.debug(f"Queue size queue_{name}: {input_queue.qsize()}")
                my_logger.info(f"{name} processor is done!")





class IssueProcessor(BaseQueueProcessor):
    """Processor for issue data with enhanced field mapping and type conversion."""
    
    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        queue_upsert_issue: Queue,
        my_logger=None
    ) -> bool:
        """Process issue DataFrame with field mapping and type conversion."""
        if df is None:
            return False
        
        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty DataFrame, queue size: {queue_upsert_issue.qsize()}")
            return True
        
        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)
            
            # Apply special data conversions first
            df = self.type_handler.handle_special_conversions(df, my_logger)
            
            # Handle description field reconstruction
            df = self._handle_description_field(df, my_logger)
            
            # Drop unwanted columns
            df = self._drop_unwanted_columns(df, my_logger)
            
            # Apply field mappings from YAML configuration
            df = self.field_mapper.apply_field_mappings(df, my_logger)
            
            # Handle initiative attributes
            df_initiative_attribute = self._extract_initiative_attributes(df, my_logger)
            
            # Apply robust type conversion
            df, conversion_results = self.type_handler.apply_field_based_type_conversion(df, my_logger)
            
            # Handle remaining manual type conversions
            df = self._handle_manual_type_conversions(df, my_logger)
            
            # Process description markdown
            if 'description_markdown' in df.columns:
                df['description_markdown'] = df['description_markdown'].apply(
                    lambda x: markdownify.markdownify(x, heading_style='ATX') if pd.notna(x) else x
                )
            
            # Handle timetracking
            df = self._handle_timetracking(df, my_logger)
            
            # Handle datetime conversions
            df = self._handle_datetime_conversions(df, my_logger)
            df['isSubTask'] = df['isSubTask'].astype(bool)

            
            # Queue main issue data
            await queue_upsert_issue.put({
                "model": Issue,
                "df": df,
                "no_update_cols": ("tscv_summary_description",),
                "on_conflict_update": True,
                "conflict_condition": ["updated"]
            })
            
            # Queue initiative attributes if any
            if df_initiative_attribute.shape[0] > 0:
                await self._queue_initiative_attributes(df_initiative_attribute, queue_upsert_issue, my_logger)
            
            # Log conversion summary
            summary = self.type_handler.get_conversion_summary()
            if summary["total_conversions"] > 0 and my_logger:
                my_logger.info(f"Type conversion summary: {summary['successful']}/{summary['total_conversions']} successful")

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing issue data: {e}", exc_info=True)
            raise
    
    def _handle_description_field(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle description field reconstruction."""
        if 'description.type' in df.columns:
            condition = df['description.type'].notna()
            df.loc[condition, 'description'] = df.loc[condition].apply(
                lambda row: {
                    'type': row['description.type'],
                    "version": row['description.version'],
                    "content": row['description.content']
                },
                axis=1
            )
        return df
    
    def _drop_unwanted_columns(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Drop unwanted columns based on configuration."""
        drop_prefixes = self.field_mapper.get_drop_column_prefixes()
        exceptions = self.field_mapper.get_drop_column_exceptions()
        
        columns_to_drop = [
            col for col in df.columns 
            if any(col.startswith(prefix) for prefix in drop_prefixes) and col not in exceptions
        ]
        
        if columns_to_drop:
            df.drop(columns=columns_to_drop, inplace=True)
            if my_logger:
                my_logger.debug(f"Dropped {len(columns_to_drop)} unwanted columns")
        
        return df
    
    def _extract_initiative_attributes(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Extract and process initiative attributes."""
        df_initiative_attribute = df[df['issuetype'] == "Initiative"].copy()
        
        if df_initiative_attribute.shape[0] > 0:
            required_columns = [
                'id', 'key', 'customfield_10182', 'customfield_10183', 'customfield_10184',
                'created', 'updated'
            ]
            
            # Add missing columns with NaN values
            for col in required_columns:
                if col not in df_initiative_attribute.columns:
                    df_initiative_attribute[col] = float('nan')
            
            df_initiative_attribute = df_initiative_attribute[required_columns]
            
            # Rename columns
            df_initiative_attribute.rename(columns={
                'id': 'initiative_id',
                'key': 'initiative_key',
                'customfield_10182': 'project',
                'customfield_10183': 'release',
                'customfield_10184': 'feature'
            }, inplace=True)
            
            # Convert initiative_id to Int64
            # df_initiative_attribute, _ = self.type_handler.safe_astype_conversion(
            #     df_initiative_attribute,
            #     {'initiative_id': pd.Int64Dtype()},
            #     my_logger
            # )

            # There is no return value
            # self.type_handler.safe_astype_conversion(
            #     df_initiative_attribute,
            #     {'initiative_id': pd.Int64Dtype()},
            #     my_logger
            # )
            df_initiative_attribute['initiative_id'] = df_initiative_attribute['initiative_id'].astype(pd.Int64Dtype())

            # Drop the original columns from main df
            columns_to_drop = ["customfield_10182", "customfield_10183", "customfield_10184"]
            existing_columns_to_drop = [col for col in columns_to_drop if col in df.columns]
            if existing_columns_to_drop:
                df.drop(columns=existing_columns_to_drop, inplace=True)
        
        return df_initiative_attribute
    
    def _handle_manual_type_conversions(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle manual type conversions that need special logic."""
        # Define columns that need special handling
        special_int_columns = ['id', 'parent_id', 'reopen_count', 'storypoints', 'issue_hierarchy_level']
        
        for col in special_int_columns:
            if col in df.columns:
                try:
                    if my_logger:
                        my_logger.debug(f"Processing special int column: {col}")
                        my_logger.debug(f"  Data type: {df[col].dtype}")
                        my_logger.debug(f"  Non-null count: {df[col].notna().sum()}/{len(df)}")

                    # Apply robust conversion
                    df, _ = self.type_handler.safe_astype_conversion(
                        df, {col: 'Int64'}, my_logger
                    )

                except Exception as e:
                    if my_logger:
                        my_logger.error(f"Failed to convert special column '{col}': {e}")
        
        return df
    
    def _handle_timetracking(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle timetracking field processing."""
        timetracking_columns = [
            'timetracking.timeSpent',
            'timetracking.remainingEstimate', 
            'timetracking.originalEstimate',
            'timetracking.timeSpentSeconds',
            'timetracking.remainingEstimateSeconds',
            'timetracking.originalEstimateSeconds'
        ]
        
        # Add missing columns
        for col in timetracking_columns:
            if col not in df.columns:
                df[col] = None
        
        # Create timetracking JSON
        df['timetracking'] = df[timetracking_columns].apply(
            lambda row: {} if row.isna().all() else {
                timetracking.split('.')[1]: row[timetracking]
                for timetracking in timetracking_columns
                if pd.notna(row[timetracking]) and row[timetracking] is not None
            },
            axis=1
        )
        
        # Drop original columns
        df.drop(columns=timetracking_columns, inplace=True)
        
        return df
    
    def _handle_datetime_conversions(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle datetime field conversions."""
        datetime_columns = ['statuscategorychangedate', 'resolutiondate', 'created', 'updated']
        existing_datetime_cols = [col for col in datetime_columns if col in df.columns]
        
        if existing_datetime_cols:
            df[existing_datetime_cols] = df[existing_datetime_cols].apply(pd.to_datetime, errors='coerce')
        
        # Handle date columns
        for col in ['startdate', 'duedate']:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors="coerce").dt.date
        
        return df
    
    async def _queue_initiative_attributes(
        self, 
        df_initiative_attribute: pd.DataFrame, 
        queue_upsert_issue: Queue, 
        my_logger
    ):
        """Queue initiative attributes for upsert."""
        # Convert datetime columns
        for col in ['created', 'updated']:
            if col in df_initiative_attribute.columns:
                df_initiative_attribute[col] = pd.to_datetime(df_initiative_attribute[col])
        
        await queue_upsert_issue.put({
            "model": InitiativeAttribute,
            "df": df_initiative_attribute,
            "primary_key": "initiative_id",
            "no_update_cols": ("attr",),
            "on_conflict_update": True,
            "conflict_condition": ["updated"]
        })


# Factory functions for creating processors
def create_issue_processor() -> IssueProcessor:
    """Create a new IssueProcessor instance."""
    return IssueProcessor()


# Refactored consume functions using the new processors
async def consume_issue_enhanced(
    queue_id: int,
        name: str,
    queue_issue: Queue,
    queue_upsert_issue: Queue,
    project_key: str,
    pg_async_session: AsyncSession,
    my_logger=None
):
    """Enhanced consume_issue function using the new processor architecture."""
    processor = create_issue_processor()
    await processor.run_processor(
        queue_id=queue_id,
        name=name,
        input_queue=queue_issue,
        queue_upsert_issue=queue_upsert_issue,
        my_logger=my_logger
    )
