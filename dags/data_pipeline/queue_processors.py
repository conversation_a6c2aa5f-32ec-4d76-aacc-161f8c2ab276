# coding=utf-8
"""
Queue processing utilities for JIRA data pipeline.

This module contains the refactored consume functions with improved
error handling, field mapping, and data type conversion.
"""
import asyncio
import sys
import traceback
from datetime import datetime
from logging import Logger

import pandas as pd
import markdownify
import aiohttp
from asyncio import Queue
from typing import Any, Optional, Dict

from dependency_injector.wiring import inject, Provide
from sqlalchemy.ext.asyncio import AsyncSession

from dags.data_pipeline.jira.api_client import fetch_with_retries_get
from dags.data_pipeline.dataframe_utils.dataframe_debug_async import quick_save_async

try:
    from containers import LoggerContainer, EntryDetails, JiraEntryDetailsContainer, KeePassContainer
    from field_mappers import get_field_mapper
    from data_type_handlers import create_robust_type_handler
    from dbmodels.issue import Issue, IssueComments, IssueLinks
    from dbmodels.changelog import ChangelogJSON
    from dbmodels.worklog import WorkLog
    from dbmodels.initiativeattribute import InitiativeAttribute
    from dags.data_pipeline.debug.debug_utils import debug_queue_operation

except ModuleNotFoundError:
    from .containers import LoggerContainer, EntryDetails, JiraEntryDetailsContainer, GlobalCircuitBreaker, \
    ApplicationContainer
    from .field_mappers import get_field_mapper
    from .data_type_handlers import create_robust_type_handler
    from .dbmodels.issue import Issue, IssueComments, IssueLinks
    from .dbmodels.changelog import ChangelogJSON
    from .dbmodels.worklog import WorkLog
    from .dbmodels.initiativeattribute import InitiativeAttribute
    from dags.data_pipeline.debug.debug_utils import debug_queue_operation
    # from .utility_code import (
    #     add_issue_key_issue_id, fetch_changelog, handle_exception,
    #     lock, commit_transaction
    # )


def add_issue_key_issue_id(d, issue_key, issue_id, my_logger: Logger):
    try:
        d['issue_key'] = issue_key
        d['issue_id'] = issue_id
        return d
    except Exception as e:
        my_logger.debug(
            f"these are the values: {issue_id} type: {type(issue_id)} -> {issue_key} type {type(issue_key)}")
        my_logger.debug(f"dict passed is : {d} type: {type(d)}")
        raise e



class BaseQueueProcessor:
    """Base class for queue processors with common functionality."""
    
    def __init__(self):
        self.field_mapper = get_field_mapper()
        self.type_handler = create_robust_type_handler()
    
    async def process_queue_item(
        self,
        item: Any,
        queue_upsert_issue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """
        Process a single queue item. To be implemented by subclasses.
        
        Args:
            item: Queue item to process
            queue_upsert_issue: Queue for upsert operations
            http_session: HTTP session for JIRA API calls
            my_logger: Logger instance
            
        Returns:
            True if processing should continue, False to stop
        """
        raise NotImplementedError("Subclasses must implement process_queue_item")
    
    async def run_processor(
        self,
        queue_id: int,
        name: str,
        input_queue: Queue,
        queue_upsert_issue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ):
        """
        Main processor loop with error handling.
        
        Args:
            queue_id: Processor ID
            name: Processor name
            input_queue: Input queue to process
            queue_upsert_issue: Output queue for upsert operations
            http_session: HTTP session for JIRA API calls
            my_logger: Logger instance
        """
        try:
            loop_count = 0
            
            while True:
                loop_count += 1
                
                try:
                    my_logger.debug(f"queue_{name} q_type: {type(input_queue)}")
                    async with debug_queue_operation(input_queue, "get", f"queue_{name}") as item:
                        should_continue = await self.process_queue_item(
                            item, queue_upsert_issue, http_session, my_logger
                        )
                        
                        if not should_continue:
                            await queue_upsert_issue.put(None)
                            break
                            
                finally:
                    async with debug_queue_operation(input_queue, "task_done", f"queue_{name}"):
                        pass
                        
        except Exception as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            my_logger.exception(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
            raise e
        finally:
            if my_logger:
                my_logger.debug(f"Queue size queue_{name}: {input_queue.qsize()}")
                my_logger.info(f"{name} processor is done!")





class ChangelogProcessor(BaseQueueProcessor):
    """Processor for changelog data with additional JIRA data fetching."""

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        queue_upsert_issue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process changelog DataFrame with additional data fetching."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty changelog DataFrame")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)

            # MISSING LOGIC: Join with changelog data and rename columns
            if df.shape[0] > 0:
                df = df.join(
                    pd.json_normalize(df.changelog)
                ).drop(columns=["changelog"])
                df.rename(columns={'id': 'issue_id', 'key': 'issue_key'}, inplace=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                _ = await quick_save_async(
                    df,
                    f"df_changelog_first_{timestamp}.xlsx",
                    path=f"c:/vishal/log/changelog"
                )

            # Fetch additional changelog data from JIRA if needed
            df = await self._fetch_additional_changelog_data(df, http_session, my_logger)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            _ = await quick_save_async(
                df,
                f"df_changelog_additional_{timestamp}.xlsx",
                path=f"c:/vishal/log/changelog"
            )

            # MISSING LOGIC: Explode histories and process changelog data
            if df.shape[0] > 0:
                df = df.explode(column="histories")
                df.dropna(subset=["histories"], inplace=True)

                # Add issue_key and issue_id to histories
                df.histories = df.apply(
                    lambda x: add_issue_key_issue_id(x['histories'], x['issue_key'], x['issue_id'], my_logger), axis=1
                )

                # Drop unnecessary columns and normalize JSON
                df.drop(columns=["startAt", "maxResults", "total", "issue_id", "issue_key"], inplace=True)
                df = pd.json_normalize(df.histories)[
                    ["id", "created", "author.accountId", "items", "issue_key", "issue_id"]]

                # Rename columns
                df.rename(columns={"author.accountId": "author"}, inplace=True)

            # Apply field mappings and type conversions
            df = self.field_mapper.apply_field_mappings(df, my_logger)
            df, conversion_results = self.type_handler.apply_field_based_type_conversion(df, my_logger)
            my_logger.info(f"Conversion results: {conversion_results}")
            _ = await quick_save_async(
                df,
                f"df_changelog_mapped_{timestamp}.xlsx",
                path=f"c:/vishal/log/changelog"
            )


            # Queue changelog data
            await queue_upsert_issue.put({
                "model": ChangelogJSON,
                "df": df,
                "on_conflict_update": False,
                "no_update_cols": ("items",)
            })

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing changelog data: {e}", exc_info=True)
            raise

    async def _fetch_additional_changelog_data(
        self,
        df: pd.DataFrame,
        http_session: aiohttp.ClientSession,
        my_logger
    ) -> pd.DataFrame:
        """Fetch additional changelog data from JIRA based on conditions."""

        if 'issue_key' not in df.columns or 'total' not in df.columns:
            if my_logger:
                my_logger.debug("No issue_key or total column found, skipping additional data fetch")
            return df

        # Create tasks for fetching additional changelog data
        # Similar to the original logic in consume_changelog_old
        changelog_records = df[['issue_id', 'issue_key', 'total']].to_dict('records')

        async def fetch_changelog_data(issue_id, issue_key, start_at):
            """Inner function to fetch changelog data for a specific range."""
            """
                    Inner function to fetch changelog data for a specific range.
                    Enhanced with proper error handling from original code.
                    """
            asyncio.current_task().set_name("fetch_changelog_data")
            base_result = {
                "issue_id": issue_id,
                "issue_key": issue_key,
                "start_at": start_at,
                "success": False,
                "error": None
            }
            try:
                changelog = await fetch_changelog(
                    http_session, issue_key, params={'startAt': start_at}, my_logger=my_logger
                )
                if not changelog.get('success', False):
                    error_msg = changelog.get('exception', 'Unknown error from fetch_changelog')
                    if my_logger:
                        my_logger.error(f"Failed to fetch changelog for {issue_key} (start_at={start_at}): {error_msg}")

                    base_result.update({
                        "error": error_msg,
                        "startAt": start_at,
                        "maxResults": 0,
                        "total": 0,
                        "histories": []
                    })
                    return base_result
                    # return {
                    #     "issue_id": issue_id,
                    #     "issue_key": issue_key,
                    #     "error": error_msg,
                    #     "startAt": start_at,
                    #     "maxResults": 0,
                    #     "total": 0,
                    #     "histories": []
                    # }

                # Success case - extract the data
                result_data = changelog.get('result', {})
                base_result.update({
                    "success": True,
                    "startAt": result_data.get('startAt', start_at),
                    "maxResults": result_data.get('maxResults', 0),
                    "total": result_data.get('total', 0),
                    "histories": result_data.get('values', [])
                })
                return base_result

            except aiohttp.ClientResponseError as err:
                error_msg = f"HTTP error {err.status}: {err.message}"
                my_logger.error(f"ClientResponseError for {issue_key} (start_at={start_at}): {error_msg}")
                base_result.update({
                    "error": error_msg,
                    "startAt": start_at,
                    "maxResults": 0,
                    "total": 0,
                    "histories": []
                })
                return base_result

            except asyncio.TimeoutError as err:
                error_msg = f"Request timeout: {str(err)}"
                my_logger.error(f"Timeout error for {issue_key} (start_at={start_at}): {error_msg}")
                base_result.update({
                    "error": error_msg,
                    "startAt": start_at,
                    "maxResults": 0,
                    "total": 0,
                    "histories": []
                })
                return base_result

            except Exception as err:
                error_msg = f"Unexpected error: {str(err)}"
                if my_logger:
                    my_logger.error(f"Unexpected error for {issue_key} (start_at={start_at}): {error_msg}")
                base_result.update({
                    "error": error_msg,
                    "startAt": start_at,
                    "maxResults": 0,
                    "total": 0,
                    "histories": []
                })
                return base_result
            finally:
                my_logger.info(f"Processed changelog fetch for {issue_key} (start_at={start_at})")

        # Create tasks for fetching changelog data
        # changelog endpoint returns changelogs in ascending order
        # issue search returns changelogs in descending order.
        # Therefore, start range needs to be 0 and end range needs to be total records - 100
        tasks_changelog = [
            fetch_changelog_data(record['issue_id'], record['issue_key'], start_at)
            for record in changelog_records
            for start_at in range(0, max(0, record['total'] - 100), 100)
        ]

        if tasks_changelog:
            if my_logger:
                my_logger.info(f"Fetching additional changelog data for {len(tasks_changelog)} requests")

            # Execute all tasks concurrently
            changelog_results = await asyncio.gather(*tasks_changelog, return_exceptions=True)

            # Process results and merge with original DataFrame
            # MISSING LOGIC: Enhanced result processing with proper validation
            valid_results = []
            for result in changelog_results:
                if (isinstance(result, dict) and
                        'issue_id' in result and
                        'issue_key' in result and
                        'histories' in result):
                    valid_results.append(result)
                elif isinstance(result, Exception):
                    if my_logger:
                        my_logger.error(f"Exception in changelog fetch task: {result}")
                else:
                    if my_logger:
                        my_logger.warning(f"Invalid result: {result}")

            if valid_results:
                additional_df = pd.DataFrame(valid_results)
                # Merge or append additional data to the original DataFrame
                df = pd.concat([df, additional_df], ignore_index=True)
                if my_logger:
                    my_logger.info(f"Added {len(valid_results)} additional changelog records")

        return df


class WorklogProcessor(BaseQueueProcessor):
    """Processor for worklog data with additional JIRA data fetching."""

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        queue_upsert_issue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process worklog DataFrame with additional data fetching."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty worklog DataFrame")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)
            if 'worklog' in df.columns:
                df = df.join(
                    pd.json_normalize(df.worklog)
                ).drop(columns=["worklog"])

            if 'total' in df.columns:
                df.query("total > 0", inplace=True)

            if df.shape[0] == 0:
                if my_logger:
                    my_logger.info("No records with total > 0")
                return True

            # MISSING: Extract worklog keys that need additional fetching
            worklog_keys = []
            if 'total' in df.columns and 'maxResults' in df.columns:
                worklog_keys = df.query("total > maxResults")[['key', 'id']].to_dict(orient='records')
                if my_logger:
                    my_logger.debug(f"worklog keys = {worklog_keys}")

            # MISSING: Column cleanup and renaming
            columns_to_drop = []
            if 'startAt' in df.columns:
                columns_to_drop.append('startAt')
            if 'maxResults' in df.columns:
                columns_to_drop.append('maxResults')
            if 'total' in df.columns:
                columns_to_drop.append('total')

            if columns_to_drop:
                df.drop(columns=columns_to_drop, inplace=True)

            # MISSING: Column renaming
            rename_map = {}
            if 'id' in df.columns:
                rename_map['id'] = 'issue_id'
            if 'key' in df.columns:
                rename_map['key'] = 'issue_key'

            if rename_map:
                df.rename(columns=rename_map, inplace=True)

            # Fetch additional worklog data from JIRA if needed
            if len(worklog_keys) > 0:
                df = await self._fetch_additional_worklog_data(df, http_session, my_logger)

            # Apply field mappings and type conversions
            df = self.field_mapper.apply_field_mappings(df, my_logger)
            df, conversion_results = self.type_handler.apply_field_based_type_conversion(df, my_logger)

            # Handle datetime conversions for worklog
            datetime_columns = ['started', 'created', 'updated']
            for col in datetime_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # Queue worklog data
            await queue_upsert_issue.put({
                "model": WorkLog,
                "df": df,
                "no_update_cols": ("timeSpentHours",)
            })

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing worklog data: {e}", exc_info=True)
            raise

    async def _fetch_additional_worklog_data(
        self,
        df: pd.DataFrame,
        http_session: aiohttp.ClientSession,
        my_logger
    ) -> pd.DataFrame:
        """Fetch additional worklog data from JIRA based on conditions."""
        if 'issue_key' not in df.columns:
            if my_logger:
                my_logger.debug("No issue_key column found, skipping additional worklog data fetch")
            return df

        # Get unique issue keys that need additional worklog data
        issue_keys = df['issue_key'].dropna().unique()

        async def fetch_worklog_data(issue_key):
            """Inner function to fetch worklog data for a specific issue."""
            try:
                worklog_response = await fetch_worklog(http_session, issue_key, my_logger)
                if not worklog_response.get('success', False):
                    error_msg = worklog_response.get('exception', 'Unknown error from fetch_worklog')
                    if my_logger:
                        my_logger.error(f"Failed to fetch worklog for {issue_key}: {error_msg}")
                    return []

                # Success case - extract the worklog data
                worklogs = worklog_response.get('result', [])
                for worklog in worklogs:
                    worklog['issue_key'] = issue_key

                return worklogs

            except Exception as err:
                if my_logger:
                    my_logger.error(f"Unexpected error fetching worklog for {issue_key}: {str(err)}")
                return []

        # Create tasks for fetching worklog data
        tasks_worklog = [fetch_worklog_data(issue_key) for issue_key in issue_keys]

        if tasks_worklog:
            if my_logger:
                my_logger.info(f"Fetching additional worklog data for {len(tasks_worklog)} issues")

            # Execute all tasks concurrently
            worklog_results = await asyncio.gather(*tasks_worklog, return_exceptions=True)

            # Process results and merge with original DataFrame
            additional_data = []
            for result in worklog_results:
                if isinstance(result, Exception):
                    if my_logger:
                        my_logger.error(f"Exception in worklog fetch task: {result}")
                    continue

                if result:  # result is a list of worklogs
                    additional_data.extend(result)

            if additional_data:
                additional_df = pd.DataFrame(additional_data)
                # Merge or append additional data to the original DataFrame
                df = pd.concat([df, additional_df], ignore_index=True)
                if my_logger:
                    my_logger.info(f"Added {len(additional_data)} additional worklog records")

        return df


class CommentProcessor(BaseQueueProcessor):
    """Processor for comment data with additional JIRA data fetching."""

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        queue_upsert_issue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process comment DataFrame with additional data fetching."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty comment DataFrame")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)

            # Fetch additional comment data from JIRA if needed
            df = await self._fetch_additional_comment_data(df, http_session, my_logger)

            # Apply field mappings and type conversions
            df = self.field_mapper.apply_field_mappings(df, my_logger)
            df, conversion_results = self.type_handler.apply_field_based_type_conversion(df, my_logger)

            # Handle datetime conversions for comments
            datetime_columns = ['created', 'updated']
            for col in datetime_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # Queue comment data
            await queue_upsert_issue.put({
                "model": IssueComments,
                "df": df,

                "conflict_condition": ["updated"]
            })

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing comment data: {e}", exc_info=True)
            raise

    async def _fetch_additional_comment_data(
        self,
        df: pd.DataFrame,
        http_session: aiohttp.ClientSession,
        my_logger
    ) -> pd.DataFrame:
        """Fetch additional comment data from JIRA based on conditions."""
        if 'issue_key' not in df.columns:
            if my_logger:
                my_logger.debug("No issue_key column found, skipping additional comment data fetch")
            return df

        # Get unique issue keys that need additional comment data
        issue_keys = df['issue_key'].dropna().unique()

        async def fetch_comment_data(issue_key):
            """Inner function to fetch comment data for a specific issue."""
            try:
                comment_response = await fetch_comments(http_session, issue_key, my_logger)
                if not comment_response.get('success', False):
                    if my_logger:
                        my_logger.error(f"Failed to fetch comments for {issue_key}")
                    return []

                # Success case - extract the comment data
                comments = comment_response.get('comments', [])
                for comment in comments:
                    comment['issue_key'] = issue_key

                return comments

            except Exception as err:
                if my_logger:
                    my_logger.error(f"Unexpected error fetching comments for {issue_key}: {str(err)}")
                return []

        # Create tasks for fetching comment data
        tasks_comment = [fetch_comment_data(issue_key) for issue_key in issue_keys]

        if tasks_comment:
            if my_logger:
                my_logger.info(f"Fetching additional comment data for {len(tasks_comment)} issues")

            # Execute all tasks concurrently
            comment_results = await asyncio.gather(*tasks_comment, return_exceptions=True)

            # Process results and merge with original DataFrame
            additional_data = []
            for result in comment_results:
                if isinstance(result, Exception):
                    if my_logger:
                        my_logger.error(f"Exception in comment fetch task: {result}")
                    continue

                if result:  # result is a list of comments
                    additional_data.extend(result)

            if additional_data:
                additional_df = pd.DataFrame(additional_data)
                # Merge or append additional data to the original DataFrame
                df = pd.concat([df, additional_df], ignore_index=True)
                if my_logger:
                    my_logger.info(f"Added {len(additional_data)} additional comment records")

        return df


class IssueLinksProcessor(BaseQueueProcessor):
    """Processor for issue links data."""

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        queue_upsert_issue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process issue links DataFrame."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty issue links DataFrame")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)

            # Apply field mappings and type conversions
            df = self.field_mapper.apply_field_mappings(df, my_logger)
            df, conversion_results = self.type_handler.apply_field_based_type_conversion(df, my_logger)

            # Queue issue links data
            await queue_upsert_issue.put({
                "model": IssueLinks,
                "df": df
            })

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing issue links data: {e}", exc_info=True)
            raise


class IssueProcessor(BaseQueueProcessor):
    """Processor for issue data with enhanced field mapping and type conversion."""

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        queue_upsert_issue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process issue DataFrame with field mapping and type conversion."""
        if df is None:
            return False
        
        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty DataFrame, queue size: {queue_upsert_issue.qsize()}")
            return True
        
        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)
            
            # Apply special data conversions first
            df = self.type_handler.handle_special_conversions(df, my_logger)
            
            # Handle description field reconstruction
            df = self._handle_description_field(df, my_logger)
            
            # Drop unwanted columns
            df = self._drop_unwanted_columns(df, my_logger)
            
            # Apply field mappings from YAML configuration
            df = self.field_mapper.apply_field_mappings(df, my_logger)
            
            # Handle initiative attributes
            df_initiative_attribute = self._extract_initiative_attributes(df, my_logger)
            
            # Apply robust type conversion
            df, conversion_results = self.type_handler.apply_field_based_type_conversion(df, my_logger)
            
            # Handle remaining manual type conversions
            df = self._handle_manual_type_conversions(df, my_logger)
            
            # Process description markdown
            if 'description_markdown' in df.columns:
                df['description_markdown'] = df['description_markdown'].apply(
                    lambda x: markdownify.markdownify(x, heading_style='ATX') if pd.notna(x) else x
                )
            
            # Handle timetracking
            df = self._handle_timetracking(df, my_logger)
            
            # Handle datetime conversions
            df = self._handle_datetime_conversions(df, my_logger)
            df['isSubTask'] = df['isSubTask'].astype(bool)

            
            # Queue main issue data
            await queue_upsert_issue.put({
                "model": Issue,
                "df": df,
                "no_update_cols": ("tscv_summary_description",),
                "on_conflict_update": True,
                "conflict_condition": ["updated"]
            })
            
            # Queue initiative attributes if any
            if df_initiative_attribute.shape[0] > 0:
                await self._queue_initiative_attributes(df_initiative_attribute, queue_upsert_issue, my_logger)
            
            # Log conversion summary
            summary = self.type_handler.get_conversion_summary()
            if summary["total_conversions"] > 0 and my_logger:
                my_logger.info(f"Type conversion summary: {summary['successful']}/{summary['total_conversions']} successful")

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing issue data: {e}", exc_info=True)
            raise
    
    def _handle_description_field(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle description field reconstruction."""
        if 'description.type' in df.columns:
            condition = df['description.type'].notna()
            df.loc[condition, 'description'] = df.loc[condition].apply(
                lambda row: {
                    'type': row['description.type'],
                    "version": row['description.version'],
                    "content": row['description.content']
                },
                axis=1
            )
        return df
    
    def _drop_unwanted_columns(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Drop unwanted columns based on configuration."""
        drop_prefixes = self.field_mapper.get_drop_column_prefixes()
        exceptions = self.field_mapper.get_drop_column_exceptions()
        
        columns_to_drop = [
            col for col in df.columns 
            if any(col.startswith(prefix) for prefix in drop_prefixes) and col not in exceptions
        ]
        
        if columns_to_drop:
            df.drop(columns=columns_to_drop, inplace=True)
            if my_logger:
                my_logger.debug(f"Dropped {len(columns_to_drop)} unwanted columns")
        
        return df
    
    def _extract_initiative_attributes(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Extract and process initiative attributes."""
        df_initiative_attribute = df[df['issuetype'] == "Initiative"].copy()
        
        if df_initiative_attribute.shape[0] > 0:
            required_columns = [
                'id', 'key', 'customfield_10182', 'customfield_10183', 'customfield_10184',
                'created', 'updated'
            ]
            
            # Add missing columns with NaN values
            for col in required_columns:
                if col not in df_initiative_attribute.columns:
                    df_initiative_attribute[col] = float('nan')
            
            df_initiative_attribute = df_initiative_attribute[required_columns]
            
            # Apply initiative attributes field mapping
            initiative_mapping = self.field_mapper.get_field_mapping('initiative_attributes')
            if initiative_mapping and initiative_mapping.mapping:
                df_initiative_attribute.rename(columns=initiative_mapping.mapping, inplace=True)

            # Apply type conversions using field mapping
            df_initiative_attribute, _ = self.type_handler.apply_field_based_type_conversion(
                df_initiative_attribute, my_logger
            )

            # Drop the original columns from main df
            columns_to_drop = ["customfield_10182", "customfield_10183", "customfield_10184"]
            existing_columns_to_drop = [col for col in columns_to_drop if col in df.columns]
            if existing_columns_to_drop:
                df.drop(columns=existing_columns_to_drop, inplace=True)
        
        return df_initiative_attribute
    
    def _handle_manual_type_conversions(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle manual type conversions that need special logic."""
        # Define columns that need special handling
        special_int_columns = ['id', 'parent_id', 'reopen_count', 'storypoints', 'issue_hierarchy_level']
        
        for col in special_int_columns:
            if col in df.columns:
                try:
                    if my_logger:
                        my_logger.debug(f"Processing special int column: {col}")
                        my_logger.debug(f"  Data type: {df[col].dtype}")
                        my_logger.debug(f"  Non-null count: {df[col].notna().sum()}/{len(df)}")

                    # Apply robust conversion
                    df, _ = self.type_handler.safe_astype_conversion(
                        df, {col: 'Int64'}, my_logger
                    )

                except Exception as e:
                    if my_logger:
                        my_logger.error(f"Failed to convert special column '{col}': {e}")
        
        return df
    
    def _handle_timetracking(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle timetracking field processing."""
        timetracking_columns = [
            'timetracking.timeSpent',
            'timetracking.remainingEstimate', 
            'timetracking.originalEstimate',
            'timetracking.timeSpentSeconds',
            'timetracking.remainingEstimateSeconds',
            'timetracking.originalEstimateSeconds'
        ]
        
        # Add missing columns
        for col in timetracking_columns:
            if col not in df.columns:
                df[col] = None
        
        # Create timetracking JSON
        df['timetracking'] = df[timetracking_columns].apply(
            lambda row: {} if row.isna().all() else {
                timetracking.split('.')[1]: row[timetracking]
                for timetracking in timetracking_columns
                if pd.notna(row[timetracking]) and row[timetracking] is not None
            },
            axis=1
        )
        
        # Drop original columns
        df.drop(columns=timetracking_columns, inplace=True)
        
        return df
    
    def _handle_datetime_conversions(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle datetime field conversions."""
        datetime_columns = ['statuscategorychangedate', 'resolutiondate', 'created', 'updated']
        existing_datetime_cols = [col for col in datetime_columns if col in df.columns]
        
        if existing_datetime_cols:
            df[existing_datetime_cols] = df[existing_datetime_cols].apply(pd.to_datetime, errors='coerce')
        
        # Handle date columns
        for col in ['startdate', 'duedate']:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors="coerce").dt.date
        
        return df
    
    async def _queue_initiative_attributes(
        self, 
        df_initiative_attribute: pd.DataFrame, 
        queue_upsert_issue: Queue, 
        my_logger
    ):
        """Queue initiative attributes for upsert."""
        # Convert datetime columns
        for col in ['created', 'updated']:
            if col in df_initiative_attribute.columns:
                df_initiative_attribute[col] = pd.to_datetime(df_initiative_attribute[col])
        
        await queue_upsert_issue.put({
            "model": InitiativeAttribute,
            "df": df_initiative_attribute,
            "primary_key": "initiative_id",
            "no_update_cols": ("attr",),
            "on_conflict_update": True,
            "conflict_condition": ["updated"]
        })


# Factory functions for creating processors
def create_changelog_processor() -> ChangelogProcessor:
    """Create a new ChangelogProcessor instance."""
    return ChangelogProcessor()


def create_worklog_processor() -> WorklogProcessor:
    """Create a new WorklogProcessor instance."""
    return WorklogProcessor()


def create_comment_processor() -> CommentProcessor:
    """Create a new CommentProcessor instance."""
    return CommentProcessor()


def create_issue_links_processor() -> IssueLinksProcessor:
    """Create a new IssueLinksProcessor instance."""
    return IssueLinksProcessor()


def create_issue_processor() -> IssueProcessor:
    """Create a new IssueProcessor instance."""
    return IssueProcessor()


# Refactored consume functions using the new processors
async def consume_changelog_enhanced(
    queue_id: int,
    name: str,
    queue_changelog: Queue,
    queue_upsert_issue: Queue,
    project_key: str,
    pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_changelog function using the new processor architecture."""
    processor = create_changelog_processor()
    await processor.run_processor(
        queue_id=queue_id,
        name="changelog",
        input_queue=queue_changelog,
        queue_upsert_issue=queue_upsert_issue,
        http_session=http_session,
        my_logger=my_logger
    )


async def consume_worklog_enhanced(
    queue_id: int,
        name: str,
    queue_worklog: Queue,
    queue_upsert_issue: Queue,
    project_key: str,
    pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_worklog function using the new processor architecture."""
    processor = create_worklog_processor()
    await processor.run_processor(
        queue_id=queue_id,
        name="worklog",
        input_queue=queue_worklog,
        queue_upsert_issue=queue_upsert_issue,
        http_session=http_session,
        my_logger=my_logger
    )


async def consume_comment_enhanced(
    queue_id: int,
    queue_comment: Queue,
    queue_upsert_issue: Queue,
    project_key: str,
    pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_comment function using the new processor architecture."""
    processor = create_comment_processor()
    await processor.run_processor(
        queue_id=queue_id,
        name="comment",
        input_queue=queue_comment,
        queue_upsert_issue=queue_upsert_issue,
        http_session=http_session,
        my_logger=my_logger
    )


async def consume_issue_links_enhanced(
    queue_id: int,
        name: str,
    queue_issue_links: Queue,
    queue_upsert_issue: Queue,
    project_key: str,
    pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_issue_links function using the new processor architecture."""
    processor = create_issue_links_processor()
    await processor.run_processor(
        queue_id=queue_id,
        name="issue_links",
        input_queue=queue_issue_links,
        queue_upsert_issue=queue_upsert_issue,
        http_session=http_session,
        my_logger=my_logger
    )


async def consume_issue_enhanced(
    queue_id: int,
        name: str,
    queue_issue: Queue,
    queue_upsert_issue: Queue,
    project_key: str,
    pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_issue function using the new processor architecture."""
    processor = create_issue_processor()
    await processor.run_processor(
        queue_id=queue_id,
        name="issue",
        input_queue=queue_issue,
        queue_upsert_issue=queue_upsert_issue,
        http_session=http_session,
        my_logger=my_logger
    )


@inject
async def fetch_changelog(
        session: aiohttp.ClientSession, issue_key: str,
        params: dict,
        my_logger: Logger,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],


) -> Any:
    changelog_semaphore = asyncio.Semaphore(20)
    url = f"{jira_entry.url}/rest/api/3/issue/{issue_key}/changelog"
    try:
        async with changelog_semaphore:
            return await fetch_with_retries_get(session, url, params)
    except Exception as e:
        my_logger.error(f"Exception in fetch_changelog for {issue_key}: {e}", exc_info=True)
        return {"success": False, "exception": str(e)}


@inject
async def fetch_worklog(
        session: aiohttp.ClientSession, issue_key: str,
my_logger: Logger,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],

) -> Any:
    worklog_semaphore = asyncio.Semaphore(20)
    url = f"{jira_entry.url}/rest/api/3/issue/{issue_key}/worklog"
    params = {
        "startAt": 20,
        "maxResults": 5000
    }
    all_worklogs = []
    try:
        async with worklog_semaphore:
            while True:
                response = await fetch_with_retries_get(session, url, params)
                if not response.get("success"):
                    my_logger.error(
                        f"Failed to fetch worklogs for issue {issue_key}: {response.get('exception')}")
                    return {"success": False, "exception": response.get("exception")}
                result = response.get("result", {})
                worklogs = result.get("worklogs", [])
                all_worklogs.extend(worklogs)
                if len(worklogs) < params["maxResults"]:
                    break
                params["startAt"] += params["maxResults"]
        return {"success": True, "result": all_worklogs}
    except Exception as e:
        my_logger.error(f"Exception in fetch_worklog for {issue_key}: {e}", exc_info=True)
        return {"success": False, "exception": str(e)}


@inject
async def fetch_comments(
        session: aiohttp.ClientSession, issue_key: str,
        my_logger: Logger,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
) -> Dict[str, Any]:
    comments_semaphore = asyncio.Semaphore(10)
    url = f"{jira_entry.url}/rest/api/3/issue/{issue_key}/comment"
    params = {
        "startAt": 100,
        "maxResults": 5000
    }
    all_comments = []
    try:
        async with comments_semaphore:
            while True:
                response = await fetch_with_retries_get(session, url, params)
                my_logger.debug(f"response: {response}")
                if not response.get("success"):
                    my_logger.error(f"Failed to fetch comments for issue {issue_key}: {response.get('exception')}")
                    return {"comments": [], "success": False}
                result = response.get("result", {})
                comments = result.get("comments", [])
                all_comments.extend(comments)
                if len(comments) < params['maxResults']:
                    break
                params["startAt"] += params["maxResults"]
        return {"comments": all_comments, "success": True}
    except Exception as e:
        my_logger.error(f"Exception in fetch_comments for {issue_key}: {e}", exc_info=True)
        return {"comments": [], "success": False}

logger_container = LoggerContainer()
logger_container.wire(modules=["dags.data_pipeline.queue_processors"])
logger_container.init_resources()

keepass_container = KeePassContainer()
jira_container = JiraEntryDetailsContainer()
jira_container.wire([__name__])
jira_container.keepass.override(keepass_container.keepass_manager)