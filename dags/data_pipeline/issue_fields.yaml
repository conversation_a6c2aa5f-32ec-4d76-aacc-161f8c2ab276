fields:
  - id: aggregateprogress
    datatype: progress
    custom: false
    name: Σ Progress
  - id: aggregatetimeestimate
    datatype: number
    custom: false
    name: Σ Remaining Estimate
  - id: aggregatetimeoriginalestimate
    datatype: number
    custom: false
    name: Σ Original Estimate
  - id: aggregatetimespent
    datatype: number
    custom: false
    name: Σ Time Spent
  - id: assignee
    datatype: user
    custom: false
    name: Assignee
  - id: comment
    datatype: comments-page
    custom: false
    name: Comment
  - id: components
    datatype: array
    custom: false
    name: Components
  - id: created
    datatype: datetime
    custom: false
    name: Created
  - id: customfield_10001
    datatype: team
    custom: true
    name: Team
  - id: customfield_10006
    datatype: option
    custom: true
    name: Review Status
  - id: customfield_10015
    datatype: date
    custom: true
    name: Start date
  - id: customfield_10019
    datatype: any
    custom: true
    name: Rank
  - id: customfield_10020
    datatype: array
    custom: true
    name: Sprint
  - id: customfield_10024
    datatype: number
    custom: true
    name: Story Points
  - id: customfield_10049
    datatype: option
    custom: true
    name: Severity
  - id: customfield_10056
    datatype: option
    custom: true
    name: Issue Classification
  - id: customfield_10059
    datatype: string
    custom: true
    name: Test Case Number
  - id: customfield_10060
    datatype: string
    custom: true
    name: Test Case Suite
  - id: customfield_10061
    datatype: string
    custom: true
    name: Test Step Number
  - id: customfield_10062
    datatype: string
    custom: true
    name: Scenario Number
  - id: customfield_10067
    datatype: array
    custom: true
    name: 'Client Jira #'
  - id: customfield_10071
    datatype: option
    custom: true
    name: Initiated By
  - id: customfield_10078
    datatype: option
    custom: true
    name: Lenox Approval Status
  - id: customfield_10092
    datatype: option
    custom: true
    name: Urgency
  - id: customfield_10120
    datatype: number
    custom: true
    name: Total Effort
  - id: customfield_10121
    datatype: number
    custom: true
    name: Total Dev Effort
  - id: customfield_10122
    datatype: number
    custom: true
    name: BA Effort
  - id: customfield_10123
    datatype: number
    custom: true
    name: AD Effort
  - id: customfield_10124
    datatype: number
    custom: true
    name: RD Effort
  - id: customfield_10125
    datatype: number
    custom: true
    name: QA Effort
  - id: customfield_10126
    datatype: number
    custom: true
    name: Contingency
  - id: customfield_10146
    datatype: option
    custom: true
    name: Requirement Finalized
  - id: customfield_10147
    datatype: number
    custom: true
    name: Reopened Count
  - id: customfield_10179
    datatype: option
    custom: true
    name: Assignee Team
  - id: customfield_10182
    datatype: string
    custom: true
    name: WD Project
  - id: customfield_10183
    datatype: string
    custom: true
    name: WD Release
  - id: customfield_10184
    datatype: string
    custom: true
    name: WD Feature
  - id: customfield_10199
    datatype: number
    custom: true
    name: CVSS Score
  - id: customfield_10256
    datatype: string
    custom: true
    name: Initiative Details
  - id: description
    datatype: string
    custom: false
    name: Description
  - id: duedate
    datatype: date
    custom: false
    name: Due date
  - id: fixVersions
    datatype: array
    custom: false
    name: Fix versions
  - id: issuelinks
    datatype: array
    custom: false
    name: Linked Issues
  - id: issuetype
    datatype: issuetype
    custom: false
    name: Issue Type
  - id: parent
    datatype: array
    custom: false
    name: Parent
  - id: priority
    datatype: priority
    custom: false
    name: Priority
  - id: progress
    datatype: progress
    custom: false
    name: Progress
  - id: reporter
    datatype: user
    custom: false
    name: Reporter
  - id: resolution
    datatype: resolution
    custom: false
    name: Resolution
  - id: resolutiondate
    datatype: datetime
    custom: false
    name: Resolved
  - id: status
    datatype: status
    custom: false
    name: Status
  - id: statuscategorychangedate
    datatype: datetime
    custom: false
    name: Status Category Changed
  - id: summary
    datatype: string
    custom: false
    name: Summary
  - id: timeestimate
    datatype: number
    custom: false
    name: Remaining Estimate
  - id: timeoriginalestimate
    datatype: number
    custom: false
    name: Original estimate
  - id: timespent
    datatype: number
    custom: false
    name: Time Spent
  - id: timetracking
    datatype: timetracking
    custom: false
    name: Time tracking
  - id: updated
    datatype: datetime
    custom: false
    name: Updated
  - id: versions
    datatype: array
    custom: false
    name: Affects versions
  - id: worklog
    datatype: array
    custom: false
    name: Log Work
