[pytest]
env_files =
    .env

addopts = --sc-type sphinx --alluredir=allure_results --asyncio-mode=auto
asyncio_default_fixture_loop_scope = module    ;"function", "class", "module", "package", or "session"

testpaths =
    tests

allure_labels =
    epic
    feature
    story

sphinx_dir = ./docs


markers =
    bdd: Marker for BDD-style tests (e.g., using <PERSON><PERSON>kin)
    jira(issue_id): Link test to a Jira issue

bdd_features_base_dir = tests/features

# Add the project root to Python path
pythonpath = .