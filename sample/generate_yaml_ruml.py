import csv
from ruamel.yaml import YAM<PERSON>

def validate_rename_columns(rename_from, rename_to):
    """Validates that the number of entries in rename_from and rename_to match."""
    rename_from_list = rename_from.split(',') if rename_from else []
    rename_to_list = rename_to.split(',') if rename_to else []

    # If there are rename_from values but rename_to is empty, use id as rename_from
    if not rename_from_list and rename_to_list:
        return [{'from': rename_to, 'to': rename_to}]  # use rename_to as both from and to

    # Check if the lengths match
    if len(rename_from_list) != len(rename_to_list):
        raise ValueError("The number of elements in 'rename_from' and 'rename_to' must match.")

    # Construct list of dictionaries for renames without tuples
    rename_dict_list = []
    for from_val, to_val in zip(rename_from_list, rename_to_list):
        rename_dict_list.append({'from': from_val.strip(), 'to': to_val.strip()})

    return rename_dict_list


# Read the CSV file
with open(r'C:\Users\<USER>\Downloads\field_list.csv', mode='r', encoding='utf-8') as file:
    csv_reader = csv.DictReader(file)

    # Initialize a list to hold the fields
    fields = []

    for row in csv_reader:
        rename_from = row.get('rename_from', '').strip()
        rename_to = row.get('rename_to', '').strip()

        # Initialize base field structure with 'id' coming first
        field_data = {
            'id': row['id'],
            'datatype': row['data_type'],
            'custom': row['custom'].lower() == 'true',  # Convert string to boolean
            'name': row['name'],
        }

        if rename_from or rename_to:
            try:
                # Perform validation and get the rename mapping
                rename_mapping = validate_rename_columns(rename_from, rename_to)
                # Add rename dictionary to the field data
                field_data['rename'] = rename_mapping
            except ValueError as e:
                print(f"Skipping row {row['id']} due to error: {e}")
                continue

        # Add the field data to the fields list
        fields.append(field_data)

# Prepare the final YAML structure
yaml_data = {'fields': fields}

# Create YAML instance with safe loading and dumping
yaml = YAML(typ='safe')  # Use 'safe' instead of 'unsafe' for standard processing
yaml.default_flow_style = False  # Ensure block-style for dictionaries

# Write the list of fields to a YAML file
with open(r'C:\Users\<USER>\Downloads\issue_fields.yaml', 'w', encoding='utf-8') as yaml_file:
    yaml.dump(yaml_data, yaml_file)

print("YAML file 'issue_fields.yaml' has been created successfully.")
