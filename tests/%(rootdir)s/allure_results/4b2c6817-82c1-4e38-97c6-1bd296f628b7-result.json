{"name": "Test PostgresSessionManager operations after close", "status": "passed", "description": "Test that operations fail after manager is closed.", "start": 1751104736417, "stop": 1751104736424, "uuid": "ec57e820-922a-40e8-ba97-1e048ea9c103", "historyId": "ceac9f0658d89a4fab0390e7a9267a4a", "testCaseId": "ceac9f0658d89a4fab0390e7a9267a4a", "fullName": "tests.container.test_containers_comprehensive.TestErrorHandling#test_operations_after_close", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}