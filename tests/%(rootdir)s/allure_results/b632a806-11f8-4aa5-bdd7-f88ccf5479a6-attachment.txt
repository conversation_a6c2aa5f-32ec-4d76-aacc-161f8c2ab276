Would verify that <MagicMock name='mock.fetchone().project' id='2688358742928'> == Managed Services for column project
Would convert timezone for updated: <MagicMock name='mock.fetchone().updated' id='2688358743360'> to Asia/Kolkata
Would verify that <MagicMock name='mock.fetchone().updated' id='2688358743360'> == 2022-03-21 00:00:00+05:30 for column updated
Would verify that <MagicMock name='mock.fetchone().project' id='2688358290624'> == Managed Services for column project
Would verify that <MagicMock name='mock.fetchone().updated' id='2688358291056'> == mocked_scalar_value for column updated
Would verify that <MagicMock name='mock.fetchone().project' id='2688358168432'> == Managed Services for column project
Would verify that <MagicMock name='mock.fetchone().updated' id='2688358168000'> == mocked_scalar_value for column updated
