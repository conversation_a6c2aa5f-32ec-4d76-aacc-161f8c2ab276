{"name": "test_failed_fetch_returns_exception", "status": "passed", "description": "C:\\Users\\<USER>\\PycharmProjects\\airflow\\tests\\features\\fetch_with_retries.feature: Failed fetch returns exception", "attachments": [{"name": "stdout", "source": "d300a69e-d11f-4f6f-a1ce-397d3cd047bd-attachment.txt", "type": "text/plain"}], "start": 1751104738672, "stop": 1751104738688, "uuid": "8ae39de3-69c7-4f00-a43c-8d767c5b187d", "historyId": "a882191d61a39582b9f699bc1ec128e0", "testCaseId": "a882191d61a39582b9f699bc1ec128e0", "fullName": "tests.steps.test_fetch_steps#test_failed_fetch_returns_exception", "labels": [{"name": "parentSuite", "value": "tests.steps"}, {"name": "suite", "value": "test_fetch_steps"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.steps.test_fetch_steps"}]}