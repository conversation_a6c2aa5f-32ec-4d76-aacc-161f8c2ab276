{"name": "initiative_attribute: test upsert with conflict condition", "status": "passed", "description": "\n        Test with_conflict_condition for upsert functionality for InitiativeAttribute table.\n        ", "attachments": [{"name": "stdout", "source": "b632a806-11f8-4aa5-bdd7-f88ccf5479a6-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10346, 'TRAIN-1', '2020-03-23', '2022-03-21', 'Managed Services'], [10346, 'TRAIN-1', '2020-03-23', '2019-03-21', 'Managed Services'], [10346, 'TRAIN-1', '2020-03-23', '2020-03-23', 'Managed Services']]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BinaryExpression object at 0x00000271ED4A6C30>"}, {"name": "expected_updated", "value": "[Timestamp('2022-03-21 00:00:00+0530', tz='Asia/Kolkata'), None, None]"}, {"name": "should_update", "value": "[True, False, False]"}], "start": 1751104749923, "stop": 1751104749960, "uuid": "de624214-3f31-4c18-9c44-e44c75815afd", "historyId": "3d7dffe26f300519d7be7e90a603d70f", "testCaseId": "eca30d1044a42541a27f98f1986608cc", "fullName": "tests.upsert_conflict_condition.test_upsert_initiative_attribute.TestUpsertInitiativeAttribute#test_upsert_with_conflict_condition_initiative_attribute", "labels": [{"name": "story", "value": "Test upsert with conflict condition for initiative_attribute table"}, {"name": "feature", "value": "Upsert Conflict Condition"}, {"name": "severity", "value": "normal"}, {"name": "requirement", "value": "AIR-51"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_initiative_attribute"}, {"name": "subSuite", "value": "TestUpsertInitiativeAttribute"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_initiative_attribute"}]}