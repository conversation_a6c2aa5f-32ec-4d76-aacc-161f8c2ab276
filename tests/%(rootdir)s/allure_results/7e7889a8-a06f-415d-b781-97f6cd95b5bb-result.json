{"name": "Test EntryDetails creation", "status": "passed", "description": "Test creating EntryDetails with all fields.", "start": 1751104734992, "stop": 1751104734993, "uuid": "ca3e0750-f71e-49b9-b65f-d0d9086718d8", "historyId": "488fc987e6c1e22c41d4a0d87be8b625", "testCaseId": "488fc987e6c1e22c41d4a0d87be8b625", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_creation", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Entry Details Management"}, {"name": "feature", "value": "EntryDetails"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}