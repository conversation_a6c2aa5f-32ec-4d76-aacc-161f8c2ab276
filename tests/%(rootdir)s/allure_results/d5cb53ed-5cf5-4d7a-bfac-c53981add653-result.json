{"name": "Upsert Test for Issue Classification", "status": "passed", "description": "Test the upsert functionality considering null and non-null values in conflict conditions.", "steps": [{"name": "Iterate over test data and perform upserts", "status": "passed", "start": 1751104749988, "stop": 1751104749997}], "attachments": [{"name": "stdout", "source": "4768701f-31eb-4dbf-8534-1dd0ae98513f-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10352, 'TRAIN-6', 10346, 10347, 10349, 10352, 'TRAIN-16']]"}, {"name": "expected_updated", "value": "[None]"}, {"name": "should_update", "value": "[False]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BooleanClauseList object at 0x00000271ED45BD40>"}], "start": 1751104749987, "stop": 1751104749998, "uuid": "7691690c-9b13-4068-b836-f1e2f9d03782", "historyId": "41bca2c38758adb975f031201fcc2d93", "testCaseId": "079f09dafc6eae06e1ec1c4ee0c1442a", "fullName": "tests.upsert_conflict_condition.test_upsert_issue_classification#test_upsert_with_conflict_condition_issue_classification", "labels": [{"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_issue_classification"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_issue_classification"}]}