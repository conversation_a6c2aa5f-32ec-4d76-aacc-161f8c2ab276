{"name": "Test JiraEntryDetailsContainer Singleton behavior", "status": "passed", "description": "Test that the `jira_entry_details` is resolved as a singleton.", "start": 1751104728988, "stop": 1751104728991, "uuid": "0ad950fc-2c1c-44c1-bd51-fc54bb9c45dc", "historyId": "b294e07c534a2d66401f773b4f4d4c5f", "testCaseId": "b294e07c534a2d66401f773b4f4d4c5f", "fullName": "tests.container.jira_entry_details.test_jira_entry_details.TestJiraEntryDetailsContainer#test_jira_entry_details_singleton", "labels": [{"name": "feature", "value": "JiraEntryDetailsContainer"}, {"name": "severity", "value": "minor"}, {"name": "story", "value": "Test Jira entry details resolution"}, {"name": "parentSuite", "value": "tests.container.jira_entry_details"}, {"name": "suite", "value": "test_jira_entry_details"}, {"name": "subSuite", "value": "TestJiraEntryDetailsContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.jira_entry_details.test_jira_entry_details"}]}