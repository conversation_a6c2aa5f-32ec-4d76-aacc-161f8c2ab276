{"name": "Test build_entry_details with missing entry", "status": "passed", "description": "Test build_entry_details when entry is not found.", "start": 1751104736404, "stop": 1751104736406, "uuid": "ba6b4504-f6db-421e-9ddf-1dd255fcff47", "historyId": "6f6b1abba38909e11586d1db92274581", "testCaseId": "6f6b1abba38909e11586d1db92274581", "fullName": "tests.container.test_containers_comprehensive.TestErrorHandling#test_build_entry_details_missing_entry", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}