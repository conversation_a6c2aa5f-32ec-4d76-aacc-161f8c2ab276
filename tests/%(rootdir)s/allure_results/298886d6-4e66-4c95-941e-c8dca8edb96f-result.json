{"name": "test_container_composition", "status": "passed", "description": "Test container composition pattern.", "attachments": [{"name": "stdout", "source": "9b697813-5eb7-4b1d-8c74-8770a7ad1751-attachment.txt", "type": "text/plain"}], "start": 1751104929798, "stop": 1751104929799, "uuid": "828d2422-5a37-4c54-90db-021e6a99d259", "historyId": "2778b8c20bf5d29fc75de5044277354a", "testCaseId": "2778b8c20bf5d29fc75de5044277354a", "fullName": "tests.test_logger_refactor#test_container_composition", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "24392-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}