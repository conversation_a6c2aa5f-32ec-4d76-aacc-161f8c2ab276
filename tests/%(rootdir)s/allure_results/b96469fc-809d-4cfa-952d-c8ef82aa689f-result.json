{"name": "test_driver_versions", "status": "passed", "description": "Standalone function to test and print driver versions.", "attachments": [{"name": "stdout", "source": "38b2144a-baf0-4df7-a637-0d328340b84d-attachment.txt", "type": "text/plain"}], "start": 1751104736528, "stop": 1751104736532, "uuid": "35c08607-b00e-4513-84c4-3191fd83ee62", "historyId": "6e98db8f9a480ca356c852c970f08706", "testCaseId": "6e98db8f9a480ca356c852c970f08706", "fullName": "tests.container.test_database_drivers#test_driver_versions", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}