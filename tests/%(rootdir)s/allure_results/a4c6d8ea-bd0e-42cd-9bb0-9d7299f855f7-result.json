{"name": "Test psycopg2 driver availability", "status": "passed", "description": "Test that psycopg2 driver is available and importable.", "attachments": [{"name": "stdout", "source": "0bdba767-1d12-414e-bc3c-640db693f838-attachment.txt", "type": "text/plain"}], "start": 1751104736436, "stop": 1751104736437, "uuid": "dcf036d4-fe1c-493a-9eb5-052db14d398a", "historyId": "f22da130b98ff04199cec12f474d4841", "testCaseId": "f22da130b98ff04199cec12f474d4841", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_psycopg2_available", "labels": [{"name": "story", "value": "Driver Compatibility"}, {"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}