{"name": "Test get_jira_users using DI fixture", "status": "broken", "statusDetails": {"message": "AttributeError: 'Provide' object has no attribute 'url'", "trace": "self = <airflow.tests.test_jira_api_mocks.TestJiraUsers object at 0x000001344E839EE0>\nmock_fetch_function = <AsyncMock name='fetch_with_retries_get' id='1324186832576'>\ndi_containers = {'app_container': <dependency_injector.containers.DynamicContainer object at 0x000001344FAE74D0>, 'logger_container': ...agicMock id='1324186746272'>, 'mock_db_session': <MagicMock name='mock.session().__enter__()' id='1324186733600'>, ...}\n\n    @patch(\"dags.data_pipeline.utility_code.fetch_with_retries_get\")\n    @pytest.mark.asyncio\n    @allure.title(\"Test get_jira_users using DI fixture\")\n    async def test_get_jira_users_with_fixture(self, mock_fetch_function, di_containers):\n        \"\"\"Test using the DI fixture for clean dependency injection setup\"\"\"\n    \n        # Setup mock response\n        mock_fetch_function.return_value = {\n            \"success\": True,\n            \"result\": [\n                {\n                    \"accountId\": \"user1\",\n                    \"displayName\": \"User One\",\n                    \"emailAddress\": \"<EMAIL>\",\n                    \"active\": True,\n                    \"self\": \"https://example.atlassian.net/rest/api/3/user?accountId=user1\",\n                    \"avatarUrls\": {\n                        \"48x48\": \"https://example.atlassian.net/secure/useravatar?size=large&avatarId=10120\",\n                        \"24x24\": \"https://example.atlassian.net/secure/useravatar?size=small&avatarId=10120\",\n                    }\n                }\n            ]\n        }\n    \n        # Execute function\n>       result = await uc.get_jira_users()\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntest_jira_api_mocks.py:438: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1066: in _patched\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\dags\\data_pipeline\\utility_code.py:1166: in get_jira_users\n    handle_exception(e)\n..\\.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1089: in _patched\n    return fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^\n..\\dags\\data_pipeline\\utility_code.py:3736: in handle_exception\n    raise e\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\njira_entry = <dependency_injector.wiring.Provide object at 0x000001344E7926F0>\napp_container = <dependency_injector.containers.DynamicContainer object at 0x000001344FAE74D0>\nmy_logger = <MagicMock id='1324185932752'>\n\n    @inject\n    async def get_jira_users(\n            jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],\n            app_container: DynamicContainer = Provide[ApplicationContainer],\n            my_logger: Logger = Provide[LoggerContainer.logger]\n    ) -> str:\n        return_string = \"\"\n        try:\n            # config_dict = get_env_variables()\n            task = asyncio.current_task()\n            task.set_name(\"get_jira_users\")\n            print(f\"received jira_entry of type: {type(jira_entry)}\")\n            print(f\"received app_container of type: {type(app_container)}\")\n            print(jira_entry)\n            print(dir(app_container.database_rw()))\n    \n            tasks = []\n            endpoint = \"/rest/api/3/users\"\n>           url = f\"{jira_entry.url}{endpoint}\"\n                     ^^^^^^^^^^^^^^\nE           AttributeError: 'Provide' object has no attribute 'url'\n\n..\\dags\\data_pipeline\\utility_code.py:1119: AttributeError"}, "description": "Test using the DI fixture for clean dependency injection setup", "attachments": [{"name": "stdout", "source": "59ddf18e-63f8-4e44-80ff-ad33b141560e-attachment.txt", "type": "text/plain"}], "start": 1751091579448, "stop": 1751091579449, "uuid": "4431ab91-83e0-44eb-a9ec-f8d19fde5ba7", "historyId": "eb1ed2d1620695a227b7bfdee21dea80", "testCaseId": "eb1ed2d1620695a227b7bfdee21dea80", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_with_fixture", "labels": [{"name": "story", "value": "Get Jira Users"}, {"name": "feature", "value": "Jira API"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "24932-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}