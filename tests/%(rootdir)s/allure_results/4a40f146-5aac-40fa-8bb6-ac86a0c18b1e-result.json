{"name": "Test schema_translate_map update to different schema", "status": "passed", "description": "Test that `update_schema` correctly updates the schema_translate_map on the engines.", "start": 1751104720853, "stop": 1751104723993, "uuid": "3fa83849-1af7-469c-93cf-d600c021b098", "historyId": "00d7e3ca7cc4bd50d4907d070b1bfcac", "testCaseId": "00d7e3ca7cc4bd50d4907d070b1bfcac", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_update_schema_updates_schema_translate_map", "labels": [{"name": "story", "value": "Test against real db"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}