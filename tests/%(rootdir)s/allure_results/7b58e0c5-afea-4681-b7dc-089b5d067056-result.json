{"name": "Test get_kp_entry_details resolution for PG_RO", "status": "passed", "description": "Test that the `pg_ro` resolves correctly with the appropriate title.", "start": 1751104734187, "stop": 1751104734210, "uuid": "ea0f6585-ffcd-45cc-80c4-4257b04ebc4f", "historyId": "9d7d858dd056a153d91441cb5ce22601", "testCaseId": "9d7d858dd056a153d91441cb5ce22601", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_pg_ro_entry_details", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}