{"name": "test_successful_fetch_returns_result", "status": "passed", "description": "C:\\Users\\<USER>\\PycharmProjects\\airflow\\tests\\features\\fetch_with_retries.feature: Successful fetch returns result", "attachments": [{"name": "stdout", "source": "da4c43a3-4975-4e50-bd84-e3d6eb9a7936-attachment.txt", "type": "text/plain"}], "start": 1751104738634, "stop": 1751104738648, "uuid": "c7a719fe-6aa4-4fbf-b9ef-ce9367404911", "historyId": "30881ec731ea11d5f28f7606404380db", "testCaseId": "30881ec731ea11d5f28f7606404380db", "fullName": "tests.steps.test_fetch_steps#test_successful_fetch_returns_result", "labels": [{"name": "parentSuite", "value": "tests.steps"}, {"name": "suite", "value": "test_fetch_steps"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.steps.test_fetch_steps"}]}