{"name": "Test SQLAlchemy engine creation with mocked psycopg2", "status": "passed", "description": "Test SQLAlchemy engine creation with psycopg2 (mocked).", "attachments": [{"name": "stdout", "source": "c440f991-2ef2-4920-a934-a2180c1a2a38-attachment.txt", "type": "text/plain"}], "start": 1751104736501, "stop": 1751104736504, "uuid": "906558dc-2191-48e2-8869-950dd5a25dc1", "historyId": "33005b3b45e1e472468419dae881ce48", "testCaseId": "33005b3b45e1e472468419dae881ce48", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_engine_creation_psycopg2", "labels": [{"name": "story", "value": "Driver Compatibility"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Database Drivers"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}