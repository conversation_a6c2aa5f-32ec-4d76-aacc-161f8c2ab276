{"name": "test_safe_response_access_patterns", "status": "passed", "description": "Test that our fixed code patterns safely access response data", "attachments": [{"name": "stdout", "source": "32749f83-b844-4944-8516-f66bfe7b8efe-attachment.txt", "type": "text/plain"}], "start": 1751102444311, "stop": 1751102444311, "uuid": "f5438b9b-5bd1-4abf-8719-7b9f2513c1bb", "historyId": "11089639a12418c3ddda10daa9f6c1eb", "testCaseId": "11089639a12418c3ddda10daa9f6c1eb", "fullName": "tests.test_fetch_with_retries_consistency#test_safe_response_access_patterns", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "20548-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}