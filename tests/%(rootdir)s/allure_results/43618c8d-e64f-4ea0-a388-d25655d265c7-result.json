{"name": "test_container_composition", "status": "passed", "description": "Test container composition pattern.", "attachments": [{"name": "stdout", "source": "f419c999-ba03-4967-bd18-bda6cf6862cb-attachment.txt", "type": "text/plain"}], "start": 1751104804779, "stop": 1751104804784, "uuid": "7dd33374-e022-420b-991a-f4a5fb89a6da", "historyId": "2778b8c20bf5d29fc75de5044277354a", "testCaseId": "2778b8c20bf5d29fc75de5044277354a", "fullName": "tests.test_logger_refactor#test_container_composition", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8332-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}