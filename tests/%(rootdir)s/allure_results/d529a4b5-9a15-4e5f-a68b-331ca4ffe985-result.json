{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Extract field IDs for datatype 'progress'", "status": "passed", "start": 1751104728453, "stop": 1751104728453}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751104728453, "stop": 1751104728453}], "parameters": [{"name": "datatype", "value": "'progress'"}, {"name": "expected_ids", "value": "['aggregateprogress', 'progress']"}], "start": 1751104728453, "stop": 1751104728454, "uuid": "ae259e0e-0a9c-4dc5-ae78-20b0aba19d06", "historyId": "5ab496a42d0c34e4fdee3d11dd254509", "testCaseId": "a22222b2720a766a127ad4fd3b149427", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}