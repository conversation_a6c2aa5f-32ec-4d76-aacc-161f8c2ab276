{"name": "Test dependency injection basics", "status": "passed", "description": "Test basic dependency injection functionality.", "attachments": [{"name": "stdout", "source": "ecb43945-9659-4c0c-82d6-6be559641990-attachment.txt", "type": "text/plain"}], "start": 1751104734936, "stop": 1751104734938, "uuid": "11dea3cc-1109-4c43-a7cf-ad3cd6e03f8e", "historyId": "351fdb5de703a5d815df1db26de2291b", "testCaseId": "351fdb5de703a5d815df1db26de2291b", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_dependency_injection_basics", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}