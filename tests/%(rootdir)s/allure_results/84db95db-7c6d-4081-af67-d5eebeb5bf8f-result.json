{"name": "test_dependency_injection_pattern", "status": "passed", "description": "Test dependency injection pattern with logger.", "attachments": [{"name": "stdout", "source": "a052baec-fcad-4ce3-949a-5f556d9e5d9a-attachment.txt", "type": "text/plain"}], "start": 1751104929805, "stop": 1751104929806, "uuid": "0daec0da-78d0-43a2-ba21-4d45d2878745", "historyId": "313a8f97e7c095b791febdb0bde3826d", "testCaseId": "313a8f97e7c095b791febdb0bde3826d", "fullName": "tests.test_logger_refactor#test_dependency_injection_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "24392-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}