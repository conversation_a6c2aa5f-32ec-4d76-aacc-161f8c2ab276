{"name": "Test Jira entry details resolution for JIRA_ENTRY", "status": "passed", "description": "Test that the `jira_entry_details` resolves correctly with the expected username and URL.", "start": 1751104728977, "stop": 1751104728981, "uuid": "591f4696-bad5-49d8-abc1-6897fea88612", "historyId": "f79231333c85f303d30dacb866300a48", "testCaseId": "f79231333c85f303d30dacb866300a48", "fullName": "tests.container.jira_entry_details.test_jira_entry_details.TestJiraEntryDetailsContainer#test_jira_entry_details", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "JiraEntryDetailsContainer"}, {"name": "story", "value": "Test Jira entry details resolution"}, {"name": "parentSuite", "value": "tests.container.jira_entry_details"}, {"name": "suite", "value": "test_jira_entry_details"}, {"name": "subSuite", "value": "TestJiraEntryDetailsContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.jira_entry_details.test_jira_entry_details"}]}