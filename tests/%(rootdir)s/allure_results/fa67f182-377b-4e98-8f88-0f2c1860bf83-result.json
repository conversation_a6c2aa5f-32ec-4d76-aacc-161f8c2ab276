{"name": "Test SQLAlchemy URL creation with asyncpg", "status": "passed", "description": "Test SQLAlchemy URL creation with asyncpg driver.", "attachments": [{"name": "stdout", "source": "c037e2ff-3e81-4079-94cf-56123314bb98-attachment.txt", "type": "text/plain"}], "start": 1751104736487, "stop": 1751104736491, "uuid": "4f0fe554-6d74-4d99-a384-45911043a5b2", "historyId": "b5d3023347e258e1f7d0fb7d81424f25", "testCaseId": "b5d3023347e258e1f7d0fb7d81424f25", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_url_asyncpg", "labels": [{"name": "story", "value": "Driver Compatibility"}, {"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}