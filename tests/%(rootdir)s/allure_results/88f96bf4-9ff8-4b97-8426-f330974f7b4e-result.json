{"name": "test_logger_container", "status": "passed", "description": "Test LoggerContainer initialization and functionality.", "attachments": [{"name": "stdout", "source": "28fbe6b2-2a68-4d61-91fe-9b9e5c5dac8e-attachment.txt", "type": "text/plain"}], "start": 1751104804747, "stop": 1751104804773, "uuid": "2b03afa9-6e4b-4dc0-8127-f90b04b46a26", "historyId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "testCaseId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "fullName": "tests.test_logger_refactor#test_logger_container", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8332-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}