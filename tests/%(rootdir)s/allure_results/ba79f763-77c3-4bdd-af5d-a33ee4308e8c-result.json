{"name": "Test invalid retries configuration", "status": "passed", "description": "Tests that the function raises ValueError for invalid retry configurations.", "attachments": [{"name": "Invalid Retries Test", "source": "d9ac4448-46b8-49ef-88b5-08ffd9803437-attachment.txt", "type": "text/plain"}], "start": 1751104738623, "stop": 1751104738625, "uuid": "c74a8851-b9a9-4c98-a3a5-32921a79aea8", "historyId": "4c941c6537b3e952e6e9cc84b1f8bf4f", "testCaseId": "4c941c6537b3e952e6e9cc84b1f8bf4f", "fullName": "tests.smart_retry.test_smart_retry#test_invalid_retries", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}