{"name": "Test container initialization", "status": "passed", "description": "Test DatabaseSessionManagerContainer initialization.", "start": 1751104735174, "stop": 1751104735496, "uuid": "92852beb-eedf-4803-9b14-ec0e8bc97e80", "historyId": "47fb875795c838bf2b5243f61d43b93d", "testCaseId": "47fb875795c838bf2b5243f61d43b93d", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_container_initialization", "labels": [{"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}