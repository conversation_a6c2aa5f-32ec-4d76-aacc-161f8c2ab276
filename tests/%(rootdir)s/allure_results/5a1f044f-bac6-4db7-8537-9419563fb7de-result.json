{"name": "test_configuration_pattern", "status": "passed", "description": "Test configuration management pattern.", "attachments": [{"name": "stdout", "source": "b1894fe2-b870-449a-8f95-7ad24b1d4ce9-attachment.txt", "type": "text/plain"}], "start": 1751104929812, "stop": 1751104929815, "uuid": "4ec2231f-d238-4b41-bcf0-83b33853e843", "historyId": "1df96e89cb68c22ef905bab9d6a5eb2a", "testCaseId": "1df96e89cb68c22ef905bab9d6a5eb2a", "fullName": "tests.test_logger_refactor#test_configuration_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "24392-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}