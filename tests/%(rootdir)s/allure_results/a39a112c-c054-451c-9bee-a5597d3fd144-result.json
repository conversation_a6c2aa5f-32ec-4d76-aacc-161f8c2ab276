{"name": "test_dependency_injection_pattern", "status": "passed", "description": "Test dependency injection pattern with logger.", "attachments": [{"name": "stdout", "source": "d34b51f7-bac2-48ce-891a-d214c95b5015-attachment.txt", "type": "text/plain"}], "start": 1751104749888, "stop": 1751104749893, "uuid": "f60e6c21-71a9-4f45-b27f-58efc1561f18", "historyId": "313a8f97e7c095b791febdb0bde3826d", "testCaseId": "313a8f97e7c095b791febdb0bde3826d", "fullName": "tests.test_logger_refactor#test_dependency_injection_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}