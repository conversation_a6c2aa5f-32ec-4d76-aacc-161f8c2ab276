{"name": "Test get_jira_users with simplified DI approach", "status": "passed", "description": "Simplified test using proper dependency injection pattern", "attachments": [{"name": "stdout", "source": "*************-4b0f-93ce-7a83b9afc067-attachment.txt", "type": "text/plain"}], "start": 1751104743805, "stop": 1751104744629, "uuid": "b375fdba-59ab-4020-ad6a-f89a90544ec0", "historyId": "abd1892a59abff8c04642f5eaf2d2e70", "testCaseId": "abd1892a59abff8c04642f5eaf2d2e70", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_simplified", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}