{"name": "Test database_rw session manager resolution", "status": "passed", "description": "Test that `database_rw` resolves correctly.", "start": 1751104715490, "stop": 1751104716944, "uuid": "1406ad01-da62-41b6-8c20-ef2eb07c1b30", "historyId": "15b52c4a1d7cfac51f18773d07adcdb3", "testCaseId": "15b52c4a1d7cfac51f18773d07adcdb3", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_database_plat_rw", "labels": [{"name": "story", "value": "Test against real db"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}