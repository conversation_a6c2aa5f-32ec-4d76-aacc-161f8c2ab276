{"name": "Upsert Test for Issue Classification", "status": "passed", "description": "Test the upsert functionality considering null and non-null values in conflict conditions.", "steps": [{"name": "Iterate over test data and perform upserts", "status": "passed", "start": 1751104750022, "stop": 1751104750036}], "attachments": [{"name": "log", "source": "5b248603-de9b-4a98-bab6-a6e581e66691-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "8d4a1a10-7b16-4b3b-8339-6e4176d4e649-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10352, 'TRAIN-6', None, 10347, 10349, 10352, 'TRAIN-16']]"}, {"name": "expected_updated", "value": "['TRAIN-16']"}, {"name": "should_update", "value": "[True]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BooleanClauseList object at 0x00000271ED50A9F0>"}], "start": 1751104750022, "stop": 1751104750037, "uuid": "c112df30-265e-4516-b4aa-51cacc509015", "historyId": "90521e60d05c2a4e078b7b03e157f8e0", "testCaseId": "079f09dafc6eae06e1ec1c4ee0c1442a", "fullName": "tests.upsert_conflict_condition.test_upsert_issue_classification#test_upsert_with_conflict_condition_issue_classification", "labels": [{"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_issue_classification"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_issue_classification"}]}