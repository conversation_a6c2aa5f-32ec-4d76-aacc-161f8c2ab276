{"name": "Test database provider resolution", "status": "passed", "description": "Test that database providers resolve correctly.", "start": 1751104735716, "stop": 1751104735925, "uuid": "fd7ade4d-53a1-4eef-9bb4-7c093dcfadca", "historyId": "979f2ad82379efb258a9a431b7ed212b", "testCaseId": "979f2ad82379efb258a9a431b7ed212b", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_database_provider_resolution", "labels": [{"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}