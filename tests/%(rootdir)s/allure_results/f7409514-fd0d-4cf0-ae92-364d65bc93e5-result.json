{"name": "Test get_jira_users using DI fixture", "status": "passed", "description": "Test using the DI fixture for clean dependency injection setup", "attachments": [{"name": "stdout", "source": "407f8a77-d74f-4a4c-9836-cbcb8bcfb77e-attachment.txt", "type": "text/plain"}], "start": 1751104745335, "stop": 1751104745353, "uuid": "40538b19-b90b-4e5b-af6f-c206e98db034", "historyId": "eb1ed2d1620695a227b7bfdee21dea80", "testCaseId": "eb1ed2d1620695a227b7bfdee21dea80", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_with_fixture", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}