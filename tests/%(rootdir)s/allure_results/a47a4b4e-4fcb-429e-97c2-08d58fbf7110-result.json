{"name": "Test queue selection for \"plat\" schema", "status": "passed", "description": "Test that the 'plat' schema returns the correct queue instances.", "start": 1751104734698, "stop": 1751104734700, "uuid": "8056d636-0e22-4db4-be1a-59566afba17a", "historyId": "12b2ff5229c222155457cead0b9aecde", "testCaseId": "12b2ff5229c222155457cead0b9aecde", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_queue_selector_plat", "labels": [{"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "epic", "value": "Queue Handling"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "story", "value": "Test queue selection for different schemas"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/plat-schema", "name": "http://testcase-link.com/plat-schema"}]}