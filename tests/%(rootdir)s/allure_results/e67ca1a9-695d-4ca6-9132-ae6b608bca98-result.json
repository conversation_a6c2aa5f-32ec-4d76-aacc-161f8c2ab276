{"name": "test_url_creation", "status": "passed", "description": "Test SQLAlchemy URL creation.", "attachments": [{"name": "stdout", "source": "39cc533d-6467-405e-8728-092e8a3df65c-attachment.txt", "type": "text/plain"}], "start": 1751104734742, "stop": 1751104734744, "uuid": "abc72e39-14fb-4b5f-ac05-ae31e866e523", "historyId": "899ddf681adcb4365688f9e4ed326419", "testCaseId": "899ddf681adcb4365688f9e4ed326419", "fullName": "tests.container.quick_test#test_url_creation", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "quick_test"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.quick_test"}]}