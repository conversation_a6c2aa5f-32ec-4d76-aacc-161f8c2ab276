{"name": "Test async function max retries limit", "status": "passed", "description": "Tests that the async function raises TimeoutError after reaching the max retries limit.", "start": 1751104737993, "stop": 1751104738612, "uuid": "c4e37bd6-a343-4e31-a475-6f0055f08335", "historyId": "7a3c69592401c9dd4742d6407880e28f", "testCaseId": "7a3c69592401c9dd4742d6407880e28f", "fullName": "tests.smart_retry.test_smart_retry#test_async_max_retries", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}