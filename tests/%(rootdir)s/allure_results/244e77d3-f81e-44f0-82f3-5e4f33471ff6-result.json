{"name": "Test schema update functionality", "status": "passed", "description": "Test schema update functionality.", "start": 1751104735094, "stop": 1751104735104, "uuid": "84d48960-c11f-48c0-9a6e-b9ad9ba282ea", "historyId": "8b22328d963c4804535f398ee225f666", "testCaseId": "8b22328d963c4804535f398ee225f666", "fullName": "tests.container.test_containers_comprehensive.TestPostgresSessionManager#test_update_schema", "labels": [{"name": "story", "value": "Database Session Management"}, {"name": "feature", "value": "PostgresSessionManager"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}