{"name": "Test with_database_cleanup decorator", "status": "passed", "description": "Test with_database_cleanup decorator.", "start": 1751104736382, "stop": 1751104736384, "uuid": "bc902201-7249-468f-9416-cac1027290ed", "historyId": "5a3b4cf5cad0af3136e4b74300bdfd9b", "testCaseId": "5a3b4cf5cad0af3136e4b74300bdfd9b", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_with_database_cleanup_decorator", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}