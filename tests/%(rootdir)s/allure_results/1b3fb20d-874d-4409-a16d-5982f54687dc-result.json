{"name": "Test queue selection for \"plp\" schema", "status": "passed", "description": "Test that the 'plp' schema returns the correct queue instances.", "start": 1751104734682, "stop": 1751104734683, "uuid": "5abfd41f-0830-465f-b201-2a38aa4a1a26", "historyId": "95aa2773800d7ea810191fc9d57b086a", "testCaseId": "95aa2773800d7ea810191fc9d57b086a", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_queue_selector_plp", "labels": [{"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "epic", "value": "Queue Handling"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "story", "value": "Test queue selection for different schemas"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/plp-schema", "name": "http://testcase-link.com/plp-schema"}]}