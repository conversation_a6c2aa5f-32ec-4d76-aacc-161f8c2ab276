{"name": "test_fetch_with_retries_failure", "status": "passed", "description": "Test that failed responses have consistent format", "attachments": [{"name": "stdout", "source": "02369b3d-e768-4ef3-bf55-2f9fa02adba2-attachment.txt", "type": "text/plain"}], "start": 1751104738735, "stop": 1751104738741, "uuid": "671ba67c-984c-4968-a852-c4213c36335e", "historyId": "df34c252c1894c3c42fa82c6dc510698", "testCaseId": "df34c252c1894c3c42fa82c6dc510698", "fullName": "tests.test_fetch_with_retries_consistency#test_fetch_with_retries_failure", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}