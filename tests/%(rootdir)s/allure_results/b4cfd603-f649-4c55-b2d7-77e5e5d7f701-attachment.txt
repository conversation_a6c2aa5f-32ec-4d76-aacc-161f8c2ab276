received jira_entry of type: <class 'unittest.mock.MagicMock'>
received app_container of type: <class 'dependency_injector.containers.DynamicContainer'>
<MagicMock id='2688355315936'>
['assert_any_call', 'assert_called', 'assert_called_once', 'assert_called_once_with', 'assert_called_with', 'assert_has_calls', 'assert_not_called', 'attach_mock', 'call_args', 'call_args_list', 'call_count', 'called', 'configure_mock', 'method_calls', 'mock_add_spec', 'mock_calls', 'reset_mock', 'return_value', 'session', 'side_effect']
Test completed successfully with result: +3
