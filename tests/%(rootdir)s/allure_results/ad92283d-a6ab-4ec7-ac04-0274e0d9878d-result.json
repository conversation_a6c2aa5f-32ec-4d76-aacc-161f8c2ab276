{"name": "test_configuration_pattern", "status": "passed", "description": "Test configuration management pattern.", "attachments": [{"name": "stdout", "source": "d9889041-febd-488a-832c-1ebacc318eb1-attachment.txt", "type": "text/plain"}], "start": 1751104749904, "stop": 1751104749907, "uuid": "c1ba853c-be0c-4679-888f-7247c41fbfe0", "historyId": "1df96e89cb68c22ef905bab9d6a5eb2a", "testCaseId": "1df96e89cb68c22ef905bab9d6a5eb2a", "fullName": "tests.test_logger_refactor#test_configuration_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}