{"name": "Test psycopg3 is not installed", "status": "passed", "description": "Test that psycopg3 (psycopg) is not installed to avoid conflicts.", "attachments": [{"name": "stdout", "source": "ae7eed67-c81a-449f-ad38-1eab8e1776e2-attachment.txt", "type": "text/plain"}], "start": 1751104736461, "stop": 1751104736466, "uuid": "13888b7b-c7d8-40aa-9c68-b6141cd465b1", "historyId": "8016d8d7a23430848fd1fc23ccedc42d", "testCaseId": "8016d8d7a23430848fd1fc23ccedc42d", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_psycopg3_not_installed", "labels": [{"name": "story", "value": "Driver Compatibility"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Database Drivers"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}