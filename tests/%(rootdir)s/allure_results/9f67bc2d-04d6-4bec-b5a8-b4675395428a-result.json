{"name": "test_psycopg3_conflict", "status": "passed", "description": "Test if psycopg3 is installed (should not be).", "attachments": [{"name": "stdout", "source": "bcb637d0-a6b5-4fad-ab94-301e85003b88-attachment.txt", "type": "text/plain"}], "start": 1751104734766, "stop": 1751104734773, "uuid": "6be79f35-a7de-4170-b87d-f86c3dcb23ac", "historyId": "ba4f6d37eb420a3ca20b332c62b9a5e6", "testCaseId": "ba4f6d37eb420a3ca20b332c62b9a5e6", "fullName": "tests.container.quick_test#test_psycopg3_conflict", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "quick_test"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.quick_test"}]}