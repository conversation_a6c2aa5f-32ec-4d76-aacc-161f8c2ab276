{"name": "Test async function success after retries", "status": "passed", "description": "Tests that the async function successfully completes after retrying due to deadlocks.", "attachments": [{"name": "Async Retry Attempts", "source": "862539d5-6c90-45c4-b561-470366c0e3ff-attachment.txt", "type": "text/plain"}], "start": 1751104737570, "stop": 1751104737979, "uuid": "4cac545f-e288-44d4-a2fc-d9049bd50e96", "historyId": "5fa3a9df77cf7f9c814ef156472e7c41", "testCaseId": "5fa3a9df77cf7f9c814ef156472e7c41", "fullName": "tests.smart_retry.test_smart_retry#test_async_success", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}