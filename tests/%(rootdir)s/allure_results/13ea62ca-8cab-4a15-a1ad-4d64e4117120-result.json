{"name": "Test pg_ro_entry resolution", "status": "passed", "description": "Test that `pg_ro_entry` resolves correctly.", "start": 1751104712434, "stop": 1751104715367, "uuid": "e436ba05-3800-4983-b562-5ed606f30a7c", "historyId": "2034b186413dbb8980186902dfa71797", "testCaseId": "2034b186413dbb8980186902dfa71797", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_ro_entry", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test against real db"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}