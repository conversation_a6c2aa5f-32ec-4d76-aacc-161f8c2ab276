{"name": "Test ApplicationContainer initialization", "status": "passed", "description": "Test ApplicationContainer initialization.", "start": 1751104735937, "stop": 1751104736131, "uuid": "36ed338f-1e03-4fff-ac3e-9a1451336c0f", "historyId": "aaf428432a2a9ef49b48bab561b58ecf", "testCaseId": "aaf428432a2a9ef49b48bab561b58ecf", "fullName": "tests.container.test_containers_comprehensive.TestApplicationContainer#test_application_container_init", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}