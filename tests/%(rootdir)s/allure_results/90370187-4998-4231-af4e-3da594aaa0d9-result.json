{"name": "Test database driver imports", "status": "passed", "description": "Test that database drivers can be imported.", "attachments": [{"name": "stdout", "source": "86afa9e3-2e28-45c4-b5e6-f28b8c24e881-attachment.txt", "type": "text/plain"}], "start": 1751104734824, "stop": 1751104734828, "uuid": "842eb111-8bf7-43a7-8235-45528281ab0a", "historyId": "774c3dcbd1522e2d23647c68ce8e8171", "testCaseId": "774c3dcbd1522e2d23647c68ce8e8171", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_database_driver_imports", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "blocker"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}