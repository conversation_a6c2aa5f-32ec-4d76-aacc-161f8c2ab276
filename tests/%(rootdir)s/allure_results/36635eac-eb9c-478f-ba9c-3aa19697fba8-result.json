{"name": "Test lifecycle manager cleanup", "status": "passed", "description": "Test lifecycle manager cleanup functionality.", "start": 1751104735140, "stop": 1751104735157, "uuid": "905272db-4d03-450b-bf0e-cc7db9aa3b3b", "historyId": "1e12c6cbda6118d11ab839200435dcad", "testCaseId": "1e12c6cbda6118d11ab839200435dcad", "fullName": "tests.container.test_containers_comprehensive.TestManagedPostgresSessionManager#test_lifecycle_manager_cleanup", "labels": [{"name": "story", "value": "Managed Database Sessions"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}