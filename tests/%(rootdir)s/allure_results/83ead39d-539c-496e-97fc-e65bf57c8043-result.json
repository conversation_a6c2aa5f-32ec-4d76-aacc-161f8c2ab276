{"name": "Test get_jira_users with complete mocking", "status": "passed", "description": "Alternative approach - mock the entire function chain", "attachments": [{"name": "stdout", "source": "64416859-6e4c-467d-a85e-ec56c9f5fc77-attachment.txt", "type": "text/plain"}], "start": 1751104739647, "stop": 1751104743788, "uuid": "b3424f68-e555-491c-ab2a-fb0407a8f353", "historyId": "2b1fbbb81eea6ae8cb76b5d79593a3c3", "testCaseId": "2b1fbbb81eea6ae8cb76b5d79593a3c3", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_complete_mock", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}