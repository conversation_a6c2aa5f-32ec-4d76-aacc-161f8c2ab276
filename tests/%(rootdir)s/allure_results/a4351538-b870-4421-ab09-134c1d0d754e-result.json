{"name": "test_safe_response_access_patterns", "status": "passed", "description": "Test that our fixed code patterns safely access response data", "attachments": [{"name": "stdout", "source": "d75ed4ac-44d7-4578-8052-6a88a24d89ae-attachment.txt", "type": "text/plain"}], "start": 1751104738755, "stop": 1751104738756, "uuid": "965be399-8457-4660-a8ac-004cc53a4302", "historyId": "11089639a12418c3ddda10daa9f6c1eb", "testCaseId": "11089639a12418c3ddda10daa9f6c1eb", "fullName": "tests.test_fetch_with_retries_consistency#test_safe_response_access_patterns", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}