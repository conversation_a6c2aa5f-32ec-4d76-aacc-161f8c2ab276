{"name": "test_dependency_injection_pattern", "status": "passed", "description": "Test dependency injection pattern with logger.", "attachments": [{"name": "stdout", "source": "a87b2346-be35-4767-abf0-b6bc4287c500-attachment.txt", "type": "text/plain"}], "start": 1751104900087, "stop": 1751104900089, "uuid": "24792766-7e70-4c3f-b8c3-c405b2566555", "historyId": "313a8f97e7c095b791febdb0bde3826d", "testCaseId": "313a8f97e7c095b791febdb0bde3826d", "fullName": "tests.test_logger_refactor#test_dependency_injection_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "10588-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}