{"name": "Test sync function max retries limit", "status": "passed", "description": "Tests that the sync function raises TimeoutError after reaching the max retries limit.", "start": 1751104736959, "stop": 1751104737561, "uuid": "42892235-179f-41f0-a85c-2b66e60b10e7", "historyId": "f1e95fa5975375c3952d8ab71383d874", "testCaseId": "f1e95fa5975375c3952d8ab71383d874", "fullName": "tests.smart_retry.test_smart_retry#test_sync_max_retries", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}