{"name": "Test basic imports", "status": "passed", "description": "Test that basic required modules can be imported.", "attachments": [{"name": "stdout", "source": "ff97ef8f-453a-4b2a-9295-ce2c84d72370-attachment.txt", "type": "text/plain"}], "start": 1751104734792, "stop": 1751104734795, "uuid": "7f9b2c9d-149b-492d-aa07-b34cb2b79402", "historyId": "5bccab9908afef60fcc1a90e8d1aa589", "testCaseId": "5bccab9908afef60fcc1a90e8d1aa589", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_basic_imports", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "blocker"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}