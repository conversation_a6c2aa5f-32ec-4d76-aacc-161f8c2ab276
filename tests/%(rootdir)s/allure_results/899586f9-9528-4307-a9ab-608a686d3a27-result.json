{"name": "Test get_kp_entry_details resolution for PG_RW", "status": "passed", "description": "Test that the `pg_rw` resolves correctly with the appropriate title.", "start": 1751104734127, "stop": 1751104734154, "uuid": "97ee6c06-5ca1-4ded-b580-d903b61d1fa5", "historyId": "e944340039fce68248bb2f1b0d9f617a", "testCaseId": "e944340039fce68248bb2f1b0d9f617a", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_pg_rw_entry_details", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}