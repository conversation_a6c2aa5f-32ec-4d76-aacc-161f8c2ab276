{"name": "Test build_entry_details function", "status": "passed", "description": "Test build_entry_details function.", "start": 1751104736350, "stop": 1751104736351, "uuid": "53035fa0-6c8d-482e-8c4b-859941ce6a68", "historyId": "2058bf5ccc8cc3cacc06ee9932010ee2", "testCaseId": "2058bf5ccc8cc3cacc06ee9932010ee2", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_build_entry_details", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}