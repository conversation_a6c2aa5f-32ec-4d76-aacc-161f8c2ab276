{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Extract field IDs for datatype 'user'", "status": "passed", "start": 1751104728480, "stop": 1751104728480}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751104728480, "stop": 1751104728480}], "parameters": [{"name": "datatype", "value": "'user'"}, {"name": "expected_ids", "value": "['assignee', 'reporter']"}], "start": 1751104728480, "stop": 1751104728481, "uuid": "7c32c6df-9c1f-4853-8afd-caf29f625ae2", "historyId": "1edec8a5fb729425d9ea7d0fde006bb6", "testCaseId": "a22222b2720a766a127ad4fd3b149427", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}