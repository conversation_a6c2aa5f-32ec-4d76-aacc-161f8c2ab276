{"name": "test_configuration_pattern", "status": "passed", "description": "Test configuration management pattern.", "attachments": [{"name": "stdout", "source": "b7a908e4-023c-4543-a8b5-8e0353ec215e-attachment.txt", "type": "text/plain"}], "start": 1751104900097, "stop": 1751104900098, "uuid": "ea7530b9-68c8-4bf3-a2f1-d1f3de3a9bd8", "historyId": "1df96e89cb68c22ef905bab9d6a5eb2a", "testCaseId": "1df96e89cb68c22ef905bab9d6a5eb2a", "fullName": "tests.test_logger_refactor#test_configuration_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "10588-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}