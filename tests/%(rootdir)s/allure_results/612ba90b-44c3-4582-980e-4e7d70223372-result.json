{"name": "Test SQLAlchemy URL creation with psycopg2", "status": "passed", "description": "Test SQLAlchemy URL creation with psycopg2 driver.", "attachments": [{"name": "stdout", "source": "40588c96-876a-4b7d-9720-e7160f7f515a-attachment.txt", "type": "text/plain"}], "start": 1751104736474, "stop": 1751104736476, "uuid": "2d08fb3a-e116-4339-bb43-e9a61a022652", "historyId": "bcce6ca9b61a783705914930f8b24c03", "testCaseId": "bcce6ca9b61a783705914930f8b24c03", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_url_psycopg2", "labels": [{"name": "story", "value": "Driver Compatibility"}, {"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}