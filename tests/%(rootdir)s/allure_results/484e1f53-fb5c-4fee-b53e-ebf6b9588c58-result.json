{"name": "Test sync function success after retries", "status": "passed", "description": "Tests that the sync function successfully completes after retrying due to deadlocks.", "attachments": [{"name": "Sync Retry Attempts", "source": "3d61e2e8-ad44-4785-8a7d-9c7460e7e0cc-attachment.txt", "type": "text/plain"}], "start": 1751104736546, "stop": 1751104736950, "uuid": "70cd78ce-1273-49e1-9699-50f5d60026a0", "historyId": "d4e645d86e3313fc57a0afc12ceb0a92", "testCaseId": "d4e645d86e3313fc57a0afc12ceb0a92", "fullName": "tests.smart_retry.test_smart_retry#test_sync_success", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}