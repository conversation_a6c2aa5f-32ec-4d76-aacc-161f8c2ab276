{"name": "Test EntryDetails string representation", "status": "passed", "description": "Test string representation hides password.", "start": 1751104735052, "stop": 1751104735053, "uuid": "6d635c2a-e5ba-4e7c-a605-dba48655429e", "historyId": "ffd3c1895820e4f5727d838e33b537e6", "testCaseId": "ffd3c1895820e4f5727d838e33b537e6", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_repr", "labels": [{"name": "story", "value": "Entry Details Management"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "EntryDetails"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}