{"name": "Verify all field names are extracted correctly", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751104728743, "stop": 1751104728743}, {"name": "Extract field names", "status": "passed", "start": 1751104728743, "stop": 1751104728743}, {"name": "Verify the field names match the expected values", "status": "passed", "start": 1751104728743, "stop": 1751104728744}], "start": 1751104728743, "stop": 1751104728744, "uuid": "b2dba524-c279-4ce6-838d-b26773af952c", "historyId": "0abc7dc6613ab75d935bacfff0ef3977", "testCaseId": "0abc7dc6613ab75d935bacfff0ef3977", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_names", "labels": [{"name": "feature", "value": "FieldNameExtractor"}, {"name": "story", "value": "Extract field names"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}