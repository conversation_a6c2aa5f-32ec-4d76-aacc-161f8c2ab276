{"name": "Test singleton behavior of pg_rw_entry", "status": "passed", "description": "Test that `pg_rw_entry` is resolved as a singleton.", "start": 1751104717103, "stop": 1751104719346, "uuid": "cdae9096-e750-4bc0-8a94-70a9d6af5f74", "historyId": "835e5a349672262d82bb03f6e1bbc58f", "testCaseId": "835e5a349672262d82bb03f6e1bbc58f", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_rw_entry_singleton", "labels": [{"name": "story", "value": "Test against real db"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}