{"name": "test_container_composition", "status": "passed", "description": "Test container composition pattern.", "attachments": [{"name": "stdout", "source": "e7f4d11c-bc0e-44c3-b431-ce319ffcb0f7-attachment.txt", "type": "text/plain"}], "start": 1751104900082, "stop": 1751104900083, "uuid": "b4952385-2f57-4a51-8e92-5e509acfc35c", "historyId": "2778b8c20bf5d29fc75de5044277354a", "testCaseId": "2778b8c20bf5d29fc75de5044277354a", "fullName": "tests.test_logger_refactor#test_container_composition", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "10588-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}