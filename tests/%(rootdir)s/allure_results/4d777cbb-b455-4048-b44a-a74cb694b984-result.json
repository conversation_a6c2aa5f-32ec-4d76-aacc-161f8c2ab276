{"name": "test_fetch_with_retries_success", "status": "passed", "description": "Test that successful responses have consistent format", "attachments": [{"name": "stdout", "source": "04053e61-5a95-47d5-b512-dca092c8ef57-attachment.txt", "type": "text/plain"}], "start": 1751104738714, "stop": 1751104738719, "uuid": "80ff945d-d9b1-4cd2-be52-eab73fc58772", "historyId": "c37f1a4437e742ab070ba08b2ee81145", "testCaseId": "c37f1a4437e742ab070ba08b2ee81145", "fullName": "tests.test_fetch_with_retries_consistency#test_fetch_with_retries_success", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}