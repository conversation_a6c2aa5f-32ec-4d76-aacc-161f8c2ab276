{"name": "Test with_database_cleanup decorator for sync functions", "status": "passed", "description": "Test with_database_cleanup decorator for synchronous functions.", "start": 1751104736394, "stop": 1751104736395, "uuid": "8e0a4044-2b8c-474d-acc1-d01aaee0f9fc", "historyId": "f6f059c363048e32c0d008aa4ec22c22", "testCaseId": "f6f059c363048e32c0d008aa4ec22c22", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_with_database_cleanup_decorator_sync", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}