{"name": "Test schema_ro entry details", "status": "passed", "description": "Test that the schema_ro resolves correctly with the appropriate title.", "start": 1751104734292, "stop": 1751104734303, "uuid": "3cfcb265-ea09-4ad7-9db2-68df13198cba", "historyId": "7e25073868f414a89013281fd1a875ba", "testCaseId": "7e25073868f414a89013281fd1a875ba", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_schema_ro_entry_details", "labels": [{"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}