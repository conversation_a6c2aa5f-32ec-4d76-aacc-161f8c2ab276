{"name": "Test SQLAlchemy URL creation", "status": "passed", "description": "Test SQLAlchemy URL creation with correct drivers.", "attachments": [{"name": "stdout", "source": "7dc7f833-b5bc-4a3c-b264-82edaccea8f8-attachment.txt", "type": "text/plain"}], "start": 1751104734842, "stop": 1751104734844, "uuid": "75d22bfb-c9ab-4a8f-8940-baf8944d6964", "historyId": "53cdb10bcbd149f145c7d41d48419943", "testCaseId": "53cdb10bcbd149f145c7d41d48419943", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_sqlalchemy_url_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}