{"name": "Test KeePassManager instantiation", "status": "passed", "description": "Test the instantiation of the KeePass manager with the correct parameters.", "start": 1751104729166, "stop": 1751104734037, "uuid": "0f836fee-b105-4817-87b9-def483f606b2", "historyId": "03e0338a03b26223e82cbdc972372012", "testCaseId": "03e0338a03b26223e82cbdc972372012", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_keepass_manager", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}