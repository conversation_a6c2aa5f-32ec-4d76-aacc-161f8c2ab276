{"name": "Test queue selection for \"acq\" schema", "status": "passed", "description": "Test that the 'acq' schema returns the correct queue instances.", "start": 1751104734663, "stop": 1751104734666, "uuid": "08cf2ebb-f0b8-40eb-90de-6a78cea6585b", "historyId": "743e44683ef25faa47b63de48f52eafa", "testCaseId": "743e44683ef25faa47b63de48f52eafa", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_queue_selector_acq", "labels": [{"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "epic", "value": "Queue Handling"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "story", "value": "Test queue selection for different schemas"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/acq-schema", "name": "http://testcase-link.com/acq-schema"}]}