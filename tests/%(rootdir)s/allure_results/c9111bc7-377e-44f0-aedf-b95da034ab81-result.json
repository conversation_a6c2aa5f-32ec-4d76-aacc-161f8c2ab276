{"name": "test_response_format_consistency", "status": "passed", "description": "Test that all response formats are consistent", "attachments": [{"name": "stdout", "source": "1e22bd87-7402-47fd-a335-f0bcc1446476-attachment.txt", "type": "text/plain"}], "start": 1751104738765, "stop": 1751104738767, "uuid": "815d3335-e825-43da-9859-1052bb441a1f", "historyId": "ec011eabf47961168b72967409d1dca6", "testCaseId": "ec011eabf47961168b72967409d1dca6", "fullName": "tests.test_fetch_with_retries_consistency#test_response_format_consistency", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}