{"name": "Verify all field names are extracted correctly", "status": "passed", "steps": [{"name": "Extract field names", "status": "passed", "start": 1751104728444, "stop": 1751104728444}, {"name": "Verify the field names match the expected values", "status": "passed", "start": 1751104728444, "stop": 1751104728444}], "start": 1751104728444, "stop": 1751104728445, "uuid": "97315610-0df9-4ec1-a3b1-3403db35d35f", "historyId": "3f66928e85f8dc48f62dd97d6e24384e", "testCaseId": "3f66928e85f8dc48f62dd97d6e24384e", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_names", "labels": [{"name": "feature", "value": "FieldNameExtractor"}, {"name": "story", "value": "Extract field names"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}