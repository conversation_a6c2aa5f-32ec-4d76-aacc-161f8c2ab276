{"name": "Test auto-registration with lifecycle manager", "status": "passed", "description": "Test that ManagedPostgresSessionManager auto-registers with lifecycle manager.", "start": 1751104735119, "stop": 1751104735126, "uuid": "a4188b02-fceb-41af-96c2-f90de9ac880e", "historyId": "dd85007d2530cac89b27f511e45a4cf2", "testCaseId": "dd85007d2530cac89b27f511e45a4cf2", "fullName": "tests.container.test_containers_comprehensive.TestManagedPostgresSessionManager#test_auto_registration", "labels": [{"name": "story", "value": "Managed Database Sessions"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}