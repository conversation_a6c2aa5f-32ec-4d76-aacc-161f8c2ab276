{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751104728814, "stop": 1751104728814}, {"name": "Extract field IDs for datatype 'nonexistent'", "status": "passed", "start": 1751104728814, "stop": 1751104728814}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751104728814, "stop": 1751104728814}], "parameters": [{"name": "datatype", "value": "'nonexistent'"}, {"name": "expected_ids", "value": "[]"}], "start": 1751104728812, "stop": 1751104728815, "uuid": "cf37b673-aa3d-4e1d-92ea-9f845010150d", "historyId": "4ec475d1a1ac2044452537414302ca1f", "testCaseId": "77a2f6d4d2ec5a0725fb029808f275a1", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}