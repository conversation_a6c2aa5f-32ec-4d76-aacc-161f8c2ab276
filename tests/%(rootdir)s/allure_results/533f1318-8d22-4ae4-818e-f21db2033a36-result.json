{"name": "test_logger_container", "status": "passed", "description": "Test LoggerContainer initialization and functionality.", "attachments": [{"name": "stdout", "source": "06239a97-7411-42e6-83c2-86b391d7dfa5-attachment.txt", "type": "text/plain"}], "start": 1751104929769, "stop": 1751104929793, "uuid": "57c02950-4825-4a5d-96c7-ded5417074e0", "historyId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "testCaseId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "fullName": "tests.test_logger_refactor#test_logger_container", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "24392-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}