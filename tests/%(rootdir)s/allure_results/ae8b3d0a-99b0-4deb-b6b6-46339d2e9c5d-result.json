{"name": "Test PostgresSessionManager initialization", "status": "passed", "description": "Test PostgresSessionManager initialization.", "start": 1751104735060, "stop": 1751104735068, "uuid": "f558ff2e-2c4b-4103-bbe0-604c373f07c0", "historyId": "dbbfb647d41e9be86451bc17d071dcf3", "testCaseId": "dbbfb647d41e9be86451bc17d071dcf3", "fullName": "tests.container.test_containers_comprehensive.TestPostgresSessionManager#test_postgres_session_manager_init", "labels": [{"name": "story", "value": "Database Session Management"}, {"name": "feature", "value": "PostgresSessionManager"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}