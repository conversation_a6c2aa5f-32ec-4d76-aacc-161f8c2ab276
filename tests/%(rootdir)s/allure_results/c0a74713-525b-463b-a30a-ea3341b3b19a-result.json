{"name": "Test lifecycle manager provider", "status": "passed", "description": "Test lifecycle manager provider.", "start": 1751104736141, "stop": 1751104736340, "uuid": "ed9e535b-ef92-470c-9e71-c185669536ea", "historyId": "f4f8ba0f84fae9fa606bec72097d0ce3", "testCaseId": "f4f8ba0f84fae9fa606bec72097d0ce3", "fullName": "tests.container.test_containers_comprehensive.TestApplicationContainer#test_lifecycle_manager_provider", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}