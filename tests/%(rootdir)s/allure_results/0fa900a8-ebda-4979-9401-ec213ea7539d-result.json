{"name": "test_logger_container", "status": "passed", "description": "Test LoggerContainer initialization and functionality.", "attachments": [{"name": "stdout", "source": "a5b92773-6604-4e2a-b23d-4293130c2bfb-attachment.txt", "type": "text/plain"}], "start": 1751104749778, "stop": 1751104749863, "uuid": "e338b100-2d5f-47d6-9361-f1d920d4ab7f", "historyId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "testCaseId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "fullName": "tests.test_logger_refactor#test_logger_container", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}