{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Extract field IDs for datatype 'datetime'", "status": "passed", "start": 1751104728495, "stop": 1751104728495}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751104728495, "stop": 1751104728495}], "parameters": [{"name": "datatype", "value": "'datetime'"}, {"name": "expected_ids", "value": "['created', 'resolutiondate', 'updated', 'statuscategorychangedate']"}], "start": 1751104728495, "stop": 1751104728495, "uuid": "b9ce1356-1c58-4594-8652-e9a0ea8540d7", "historyId": "1502b0c4df74979d60d6d5e64e3f48c4", "testCaseId": "a22222b2720a766a127ad4fd3b149427", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}