{"name": "Test Kee<PERSON>ass<PERSON>ana<PERSON> behavior", "status": "passed", "description": "Test that the KeePassManager behaves as a singleton.", "start": 1751104734325, "stop": 1751104734326, "uuid": "55e46e7a-e94b-4ea0-adb3-fa42f5bf8eca", "historyId": "35ea2e5cf3db6a79c98d634fafa1724f", "testCaseId": "35ea2e5cf3db6a79c98d634fafa1724f", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_keepass_manager_singleton", "labels": [{"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}