{"name": "Test mocked engine creation", "status": "passed", "description": "Test engine creation with mocked SQLAlchemy.", "attachments": [{"name": "stdout", "source": "96a14321-ea94-43d2-8769-3d26dd52fd4c-attachment.txt", "type": "text/plain"}], "start": 1751104734864, "stop": 1751104734874, "uuid": "999b8461-9b02-447d-9551-80b3d2ec2188", "historyId": "ab8f7500848a585d8b922a2ed42d4557", "testCaseId": "ab8f7500848a585d8b922a2ed42d4557", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_mocked_engine_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}