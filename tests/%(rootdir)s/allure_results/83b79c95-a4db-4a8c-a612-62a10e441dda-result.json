{"name": "Test EntryDetailsBuilder pattern", "status": "passed", "description": "Test the builder pattern for EntryDetails.", "start": 1751104735032, "stop": 1751104735033, "uuid": "c5a42e50-6391-407d-b974-b74cc161e448", "historyId": "15084eed0a800e1f870b7019359a9d45", "testCaseId": "15084eed0a800e1f870b7019359a9d45", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_builder", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Entry Details Management"}, {"name": "feature", "value": "EntryDetails"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}