{"name": "Test database_lifecycle context manager", "status": "passed", "description": "Test database_lifecycle async context manager.", "start": 1751104736364, "stop": 1751104736368, "uuid": "5c69947b-5cc6-4f9e-8d3f-67cefa741330", "historyId": "94d812df689755263e6be08f9319f217", "testCaseId": "94d812df689755263e6be08f9319f217", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_database_lifecycle_context_manager", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Utility Functions"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}