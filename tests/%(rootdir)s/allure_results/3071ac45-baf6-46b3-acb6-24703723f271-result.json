{"name": "Test async functionality", "status": "passed", "description": "Test basic async functionality.", "attachments": [{"name": "stdout", "source": "239f6935-6992-485e-b39e-1ecad8ef34c9-attachment.txt", "type": "text/plain"}], "start": 1751104734955, "stop": 1751104734973, "uuid": "f6ce3ad6-138b-49d7-b631-0e290d790d72", "historyId": "6bb45b064c08d39069cd81cea2679faf", "testCaseId": "6bb45b064c08d39069cd81cea2679faf", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_async_functionality", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Basic Setup"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}