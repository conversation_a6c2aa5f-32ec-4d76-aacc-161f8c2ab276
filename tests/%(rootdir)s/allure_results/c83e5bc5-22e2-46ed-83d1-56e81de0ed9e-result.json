{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Extract field IDs for datatype 'nonexistent'", "status": "passed", "start": 1751104728511, "stop": 1751104728511}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751104728511, "stop": 1751104728511}], "parameters": [{"name": "datatype", "value": "'nonexistent'"}, {"name": "expected_ids", "value": "[]"}], "start": 1751104728511, "stop": 1751104728511, "uuid": "da8b603e-4be3-4430-a0ef-cb4dd3478686", "historyId": "9f67fdc9024885daef874832b9a67fcf", "testCaseId": "a22222b2720a766a127ad4fd3b149427", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}