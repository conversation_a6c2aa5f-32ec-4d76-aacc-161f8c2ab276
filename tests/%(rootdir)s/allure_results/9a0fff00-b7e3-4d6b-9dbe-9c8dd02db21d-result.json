{"name": "Test get_jira_users with mocked services", "status": "passed", "description": "Test get_jira_users with properly mocked dependencies using dependency injection", "attachments": [{"name": "stdout", "source": "b4cfd603-f649-4c55-b2d7-77e5e5d7f701-attachment.txt", "type": "text/plain"}], "start": 1751104738779, "stop": 1751104739632, "uuid": "be0c8ee7-b8b5-4ba6-b7df-8c685bbd512a", "historyId": "0fc556b318c393981f356e565c5e82b3", "testCaseId": "0fc556b318c393981f356e565c5e82b3", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}