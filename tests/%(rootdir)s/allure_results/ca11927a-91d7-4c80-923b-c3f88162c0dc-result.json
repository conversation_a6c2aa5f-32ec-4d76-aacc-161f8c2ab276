{"name": "Test get_jira_users with mocked services", "status": "broken", "statusDetails": {"message": "AttributeError: 'Provide' object has no attribute 'exception'", "trace": "session = <AsyncMock name='ClientSession().__aenter__()' id='1984377093056'>\nmethod = 'GET', url = 'https://corecard.atlassian.net/rest/api/3/users'\n\n    @inject\n    @debug_async_function(\"fetch_with_retries\")\n    async def fetch_with_retries(\n            session: aiohttp.ClientSession,\n            method: str,\n            url: str,\n            *,\n            params: Union[Dict[str, Any], None] = None,\n            json_payload: Union[Dict[str, Any], None] = None,\n            retries: int = MAX_RETRIES,\n            my_logger: Logger = Provide[LoggerContainer.logger],\n            global_circuit_breaker: GlobalCircuitBreaker = Provide[ApplicationContainer.circuit_breaker],\n    ) -> Dict[str, Any]:\n        \"\"\"\n        Perform an HTTP request with retry logic.\n    \n        :param session: aiohttp ClientSession.\n        :param method: HTTP method (e.g., 'GET', 'POST', 'PUT').\n        :param url: API endpoint URL.\n        :param params: HTTP request parameters for GET.\n        :param json_payload: JSON body for POST or PUT.\n        :param retries: Maximum number of retries.\n        :param my_logger: Logger instance.\n        :param global_circuit_breaker: Circuit breaker instance.\n        :return: Dictionary with keys:\n            - 'success': bool - True if request succeeded, False otherwise\n            - 'result': Any - Response data if success=True, None if success=False\n            - 'exception': str - Error message if success=False, not present if success=True\n        \"\"\"\n        asyncio.current_task().set_name(\"fetch_with_retries\")\n        retry_count = 0\n        retry_delay = INITIAL_RETRY_DELAY\n        global commit_transaction\n        last_exception = None\n    \n        while retry_count <= retries:\n            # Check circuit breaker before making request\n            if not await global_circuit_breaker.can_execute():\n                my_logger.warning(\"Circuit breaker is OPEN or rate limited. Waiting for recovery...\")\n                await global_circuit_breaker.wait_for_recovery(timeout=300.0)\n    \n                # Check again after waiting\n                if not await global_circuit_breaker.can_execute():\n                    my_logger.error(\"Circuit breaker still OPEN after waiting. Aborting request.\")\n                    return {\"success\": False, \"exception\": \"Circuit breaker OPEN\", \"result\": None}\n    \n            await global_circuit_breaker.enter_request()\n            request_successful = False\n            need_retry = False\n    \n            try:\n                async with debug_http_request(session, method, url):\n>                   async with session.request(\n                            method=method,\n                            url=url,\n                            params=params,\n                            json=json_payload,\n                    ) as response:\nE                   TypeError: 'coroutine' object does not support the asynchronous context manager protocol\n\n..\\dags\\data_pipeline\\utility_code.py:2355: TypeError\n\nDuring handling of the above exception, another exception occurred:\n\njira_entry = EntryDetails(username='<EMAIL>', password='****', url='https://corecard.atlassian.net', custom_prop...on': 'Basic ********************************************************************', 'Content-Type': 'application/json'})\napp_container = <dependency_injector.containers.DynamicContainer object at 0x000001CE0617F740>\nmy_logger = <dependency_injector.wiring.Provide object at 0x000001CE05FED700>\n\n    @inject\n    async def get_jira_users(\n            jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],\n            app_container: DynamicContainer = Provide[ApplicationContainer],\n            my_logger: Logger = Provide[LoggerContainer.logger]\n    ) -> str:\n        return_string = \"\"\n        try:\n            # config_dict = get_env_variables()\n            task = asyncio.current_task()\n            task.set_name(\"get_jira_users\")\n    \n            tasks = []\n            endpoint = \"/rest/api/3/users\"\n            url = f\"{jira_entry.url}{endpoint}\"\n            timeout = aiohttp.ClientTimeout(total=300)\n            async with aiohttp.ClientSession(\n                    headers=jira_entry.custom_properties,\n                timeout=timeout\n            ) as http_session:\n                for i in range(3):\n                    payload_dict = {'startAt': i * 1000, 'maxResults': 1000}\n                    tasks.append(fetch_with_retries_get(http_session, url, payload_dict))\n>               responses = await asyncio.gather(*tasks)\n                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\n..\\dags\\data_pipeline\\utility_code.py:1124: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1066: in _patched\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\dags\\data_pipeline\\utility_code.py:2512: in fetch_with_retries_get\n    return await fetch_with_retries(\n..\\.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1066: in _patched\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\dags\\data_pipeline\\debug_utils.py:35: in wrapper\n    result = await func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nsession = <AsyncMock name='ClientSession().__aenter__()' id='1984377093056'>\nmethod = 'GET', url = 'https://corecard.atlassian.net/rest/api/3/users'\n\n    @inject\n    @debug_async_function(\"fetch_with_retries\")\n    async def fetch_with_retries(\n            session: aiohttp.ClientSession,\n            method: str,\n            url: str,\n            *,\n            params: Union[Dict[str, Any], None] = None,\n            json_payload: Union[Dict[str, Any], None] = None,\n            retries: int = MAX_RETRIES,\n            my_logger: Logger = Provide[LoggerContainer.logger],\n            global_circuit_breaker: GlobalCircuitBreaker = Provide[ApplicationContainer.circuit_breaker],\n    ) -> Dict[str, Any]:\n        \"\"\"\n        Perform an HTTP request with retry logic.\n    \n        :param session: aiohttp ClientSession.\n        :param method: HTTP method (e.g., 'GET', 'POST', 'PUT').\n        :param url: API endpoint URL.\n        :param params: HTTP request parameters for GET.\n        :param json_payload: JSON body for POST or PUT.\n        :param retries: Maximum number of retries.\n        :param my_logger: Logger instance.\n        :param global_circuit_breaker: Circuit breaker instance.\n        :return: Dictionary with keys:\n            - 'success': bool - True if request succeeded, False otherwise\n            - 'result': Any - Response data if success=True, None if success=False\n            - 'exception': str - Error message if success=False, not present if success=True\n        \"\"\"\n        asyncio.current_task().set_name(\"fetch_with_retries\")\n        retry_count = 0\n        retry_delay = INITIAL_RETRY_DELAY\n        global commit_transaction\n        last_exception = None\n    \n        while retry_count <= retries:\n            # Check circuit breaker before making request\n            if not await global_circuit_breaker.can_execute():\n                my_logger.warning(\"Circuit breaker is OPEN or rate limited. Waiting for recovery...\")\n                await global_circuit_breaker.wait_for_recovery(timeout=300.0)\n    \n                # Check again after waiting\n                if not await global_circuit_breaker.can_execute():\n                    my_logger.error(\"Circuit breaker still OPEN after waiting. Aborting request.\")\n                    return {\"success\": False, \"exception\": \"Circuit breaker OPEN\", \"result\": None}\n    \n            await global_circuit_breaker.enter_request()\n            request_successful = False\n            need_retry = False\n    \n            try:\n                async with debug_http_request(session, method, url):\n                    async with session.request(\n                            method=method,\n                            url=url,\n                            params=params,\n                            json=json_payload,\n                    ) as response:\n                        if response.status in (200, 201, 204):\n                            await global_circuit_breaker.record_success()\n                            request_successful = True\n                            # Success handling\n                            if response.status == 204:\n                                my_logger.info(\"Request successful, but no content to return.\")\n                                return {\"success\": True, \"result\": None}\n                            else:\n                                if response.headers.get(\"X-RateLimit-NearLimit\") == \"true\":\n                                    wait_time = 2 ** retry_count\n                                    my_logger.warning(f\"Warning: Less than 20% of the rate limit budget remains.\")\n                                    my_logger.warning(f\"Sleeping for {wait_time} seconds before retrying.\")\n                                    await asyncio.sleep(wait_time)\n                                return {\"success\": True, \"result\": await response.json()}\n    \n                        elif response.status == 429 or \"Retry-After\" in response.headers:\n                            # Retry logic - record rate limit event\n                            retry_delay = await calculate_retry_delay(response, retry_delay)\n                            rate_limit_tracker.record_rate_limit(url, retry_delay)\n                            await global_circuit_breaker.record_rate_limit(retry_delay)\n                            need_retry = True\n                            my_logger.warning(\n                                f\"Rate limited or server unavailable. Retrying in {retry_delay / 1000:.2f} seconds. \"\n                                f\"Consecutive rate limits: {rate_limit_tracker.consecutive_rate_limits}\"\n                            )\n                            # Wait for the circuit breaker to coordinate the delay\n                            await global_circuit_breaker.wait_for_recovery()\n                        elif response.status == 503:\n                            # Service unavailable - could be a service issue\n                            retry_delay = await calculate_retry_delay(response, retry_delay)\n                            await global_circuit_breaker.record_failure()\n                            need_retry = True\n                            my_logger.warning(f\"Service unavailable (503). Will retry after backoff.\")\n                        else:\n                            # Other errors - actual failures\n                            await global_circuit_breaker.record_failure()\n                            my_logger.error(f\"Request failed with status {response.status}. url = {response.url}\")\n    \n                            async with lock:\n                                commit_transaction = False\n                            return {\"success\": False, \"exception\": f\"HTTP {response.status}\"}\n                        response.raise_for_status()\n    \n            except aiohttp.ClientResponseError as e:\n                my_logger.info(f\"HTTP error {e.status}: {e.message}\")\n                last_exception = f\"HTTP error {e.status}: {e.message}\"\n                if e.status not in (429, 503):\n                    await global_circuit_breaker.record_failure()\n                    need_retry = True  # Allow retry for retriable HTTP errors\n                else:\n                    need_retry = True\n    \n            except aiohttp.client_exceptions.ConnectionTimeoutError as e:\n                my_logger.error(f\"Request error: {e}. Retrying {retry_count}/{retries} times.\")\n                last_exception = f\"Connection timeout: {str(e)}\"\n                await global_circuit_breaker.record_failure()\n                need_retry = True\n                async with lock:\n                    commit_transaction = False\n    \n            except aiohttp.client_exceptions.ClientConnectionResetError as e:\n                my_logger.error(f\"Connection reset: {e}. Retrying {retry_count}/{retries} times.\")\n                last_exception = f\"Connection reset: {str(e)}\"\n                await global_circuit_breaker.record_failure()\n                need_retry = True\n                async with lock:\n                    commit_transaction = False\n            except (aiohttp.ClientError, asyncio.TimeoutError) as e:\n                my_logger.error(f\"Request error: {e}. Retrying {retry_count}/{retries} times.\")\n                last_exception = str(e)  # Store the exception message\n                await global_circuit_breaker.record_failure()\n                need_retry = True  # Set flag to indicate retry is needed\n    \n            except Exception as e:\n>               my_logger.error(f\"Unexpected error: {e}\")\n                ^^^^^^^^^^^^^^^\nE               AttributeError: 'Provide' object has no attribute 'error'\n\n..\\dags\\data_pipeline\\utility_code.py:2435: AttributeError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <airflow.tests.test_jira_api_mocks.TestJiraUsers object at 0x000001CE06096270>\nmock_client_session = <MagicMock name='ClientSession' id='*************'>\n\n    @patch(\"aiohttp.ClientSession\")\n    @pytest.mark.asyncio\n    @allure.title(\"Test get_jira_users with mocked services\")\n    async def test_get_jira_users(self, mock_client_session):\n        # Setup mocks\n        mock_response = AsyncMock()\n        mock_response.status = 200\n        mock_response.json.return_value = [\n            {\"accountId\": \"user1\", \"displayName\": \"User One\", \"emailAddress\": \"<EMAIL>\", \"active\": True}\n        ]\n        mock_context = AsyncMock()\n        mock_context.__aenter__.return_value = mock_response\n        mock_session = AsyncMock()\n        mock_session.request.return_value = mock_context\n        mock_client_session.return_value.__aenter__.return_value = mock_session\n    \n        # Setup DI container\n        container = ApplicationContainer()\n        container.wire(modules=[__name__])  # wire the test module\n    \n        # Mock entry details\n        mock_entry = MagicMock()\n        mock_entry.url = \"http://jira.example.com\"\n        mock_entry.custom_properties = {\"Authorization\": \"Bearer testtoken\"}\n        JiraEntryDetailsContainer.jira_entry_details.override(mock_entry)\n    \n        # Mock session manager\n        mock_pg_session = MagicMock()\n        mock_pg_session.__enter__.return_value = MagicMock()\n        mock_pg_manager = MagicMock()\n        mock_pg_manager.session.return_value = mock_pg_session\n        container.database_rw.override(lambda: mock_pg_manager)\n        container.schema.override(\"public\")\n    \n        # Mock logger\n        mock_logger = MagicMock()\n        LoggerContainer.logger.override(mock_logger)\n    \n        # Run function\n>       result = await get_jira_users()\n                 ^^^^^^^^^^^^^^^^^^^^^^\n\ntest_jira_api_mocks.py:170: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1066: in _patched\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\dags\\data_pipeline\\utility_code.py:1160: in get_jira_users\n    handle_exception(e)\n..\\.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1089: in _patched\n    return fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\ne = AttributeError(\"'Provide' object has no attribute 'error'\")\nmy_logger = <dependency_injector.wiring.Provide object at 0x000001CE05FEF1D0>\n\n    @inject\n    def handle_exception(e, my_logger: Logger = Provide[LoggerContainer.logger]):\n        exc_type, exc_value, exc_tb = sys.exc_info()\n        line_num = exc_tb.tb_lineno\n        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)\n>       my_logger.exception(\n        ^^^^^^^^^^^^^^^^^^^\n            f\"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}\",\n            exc_info=True\n        )\nE       AttributeError: 'Provide' object has no attribute 'exception'\n\n..\\dags\\data_pipeline\\utility_code.py:3726: AttributeError"}, "attachments": [{"name": "log", "source": "16950720-020a-4de7-84eb-9411ce2f21dc-attachment.txt", "type": "text/plain"}], "start": 1751085069798, "stop": 1751085070870, "uuid": "17fae916-e9ba-4fe4-8b40-c551f002d30f", "historyId": "0fc556b318c393981f356e565c5e82b3", "testCaseId": "0fc556b318c393981f356e565c5e82b3", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "26848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}