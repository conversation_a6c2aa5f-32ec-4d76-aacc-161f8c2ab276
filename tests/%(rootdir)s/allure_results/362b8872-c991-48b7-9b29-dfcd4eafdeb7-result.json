{"name": "Test schema override", "status": "passed", "description": "Test schema override functionality.", "start": 1751104735504, "stop": 1751104735709, "uuid": "7ceb9e8c-f863-48b4-b43a-b8b91681e7c0", "historyId": "f6c53843ff850bae44f17635a877c665", "testCaseId": "f6c53843ff850bae44f17635a877c665", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_schema_override", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}