{"name": "test_fetch_with_retries_failure", "status": "passed", "description": "Test that failed responses have consistent format", "attachments": [{"name": "stdout", "source": "ad3b2e16-813c-45e7-be66-39d9f7ec64cc-attachment.txt", "type": "text/plain"}], "start": 1751084696861, "stop": 1751084696865, "uuid": "5cf67a55-5723-4f1a-9aa1-8f31c4ab945d", "historyId": "df34c252c1894c3c42fa82c6dc510698", "testCaseId": "df34c252c1894c3c42fa82c6dc510698", "fullName": "tests.test_fetch_with_retries_consistency#test_fetch_with_retries_failure", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "17648-Main<PERSON>hread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}