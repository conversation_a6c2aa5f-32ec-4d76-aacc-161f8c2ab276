{"name": "Test asyncpg driver availability", "status": "passed", "description": "Test that asyncpg driver is available and importable.", "attachments": [{"name": "stdout", "source": "f11fad64-6e40-4e14-ba84-b7f129e59d81-attachment.txt", "type": "text/plain"}], "start": 1751104736449, "stop": 1751104736451, "uuid": "68f5c5ce-105f-426c-928a-5dc185298657", "historyId": "5d097559dfa9b966cd74a345e3a31e2e", "testCaseId": "5d097559dfa9b966cd74a345e3a31e2e", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_asyncpg_available", "labels": [{"name": "story", "value": "Driver Compatibility"}, {"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}