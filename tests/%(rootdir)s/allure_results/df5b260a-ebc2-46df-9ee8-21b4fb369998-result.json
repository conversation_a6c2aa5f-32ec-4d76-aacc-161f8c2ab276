{"name": "Test dataclass creation", "status": "passed", "description": "Test that dataclasses work correctly.", "attachments": [{"name": "stdout", "source": "9a6d741f-a507-4b29-a178-6ac27f0654e9-attachment.txt", "type": "text/plain"}], "start": 1751104734907, "stop": 1751104734916, "uuid": "cb3eb479-45d5-4ffd-a0e6-52f248f5f08a", "historyId": "df7a5bfac4fe527d868c9ceee9c21b78", "testCaseId": "df7a5bfac4fe527d868c9ceee9c21b78", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_dataclass_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28096-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}