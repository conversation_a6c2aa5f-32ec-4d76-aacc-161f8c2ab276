# coding=utf-8
"""
Test suite for the refactored JIRA data processing modules.

This test suite covers:
- FieldMapper functionality
- DataTypeMapper operations
- RobustDataTypeHandler error handling
- IssueProcessor data processing
- Integration between modules
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import yaml
import logging
from pathlib import Path
from unittest.mock import MagicMock, patch, AsyncMock
import allure

# Import the modules to test
from dags.data_pipeline.field_mappers import Field<PERSON><PERSON>per, FieldMapping, DataTypeMapper
from dags.data_pipeline.data_type_handlers import <PERSON>ustDataTypeHandler, TypeConversionResult
from dags.data_pipeline.queue_processors import IssueProcessor


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return MagicMock()


@pytest.fixture
def sample_field_config():
    """Sample field configuration for testing."""
    return {
        'fields': [
            {
                'id': 'customfield_10024',
                'datatype': 'number',
                'custom': True,
                'name': 'Story Points',
                'mapping': {'customfield_10024': 'storypoints'},
                'target_type': 'int64'
            },
            {
                'id': 'assignee',
                'datatype': 'user',
                'custom': False,
                'name': 'Assignee',
                'mapping': {'assignee.accountId': 'assignee'},
                'target_type': 'string'
            },
            {
                'id': 'created',
                'datatype': 'datetime',
                'custom': False,
                'name': 'Created',
                'target_type': 'datetime'
            }
        ]
    }


@pytest.fixture
def temp_config_file(sample_field_config):
    """Create a temporary configuration file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(sample_field_config, f)
        return f.name


@pytest.fixture
def sample_dataframe():
    """Sample DataFrame for testing."""
    return pd.DataFrame({
        'customfield_10024': ['5', '3', '', 'nan'],
        'assignee.accountId': ['user1', 'user2', 'user3', 'user4'],
        'created': ['2023-01-01T10:00:00Z', '2023-01-02T11:00:00Z',
                   '2023-01-03T12:00:00Z', '2023-01-04T13:00:00Z'],
        'unwanted.field': ['a', 'b', 'c', 'd'],
        'description.type': ['doc', 'doc', None, 'doc'],
        'description.version': [1, 1, None, 1],
        'description.content': ['content1', 'content2', None, 'content3'],
        'issuetype': ['Story', 'Bug', 'Initiative', 'Task']  # Add issuetype for testing
    })


@allure.feature("FieldMapper")
@allure.story("Field Mapping Configuration")
class TestFieldMapper:
    """Test suite for FieldMapper class."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test FieldMapper initialization")
    def test_field_mapper_initialization(self, temp_config_file):
        """Test FieldMapper initialization with custom config."""
        mapper = FieldMapper(temp_config_file)
        
        assert len(mapper._field_mappings) == 3
        assert 'customfield_10024' in mapper._field_mappings
        assert 'assignee' in mapper._field_mappings
        assert 'created' in mapper._field_mappings
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test column rename map generation")
    def test_column_rename_map(self, temp_config_file):
        """Test column rename map generation."""
        mapper = FieldMapper(temp_config_file)
        rename_map = mapper.get_column_rename_map()
        
        expected_mappings = {
            'customfield_10024': 'storypoints',
            'assignee.accountId': 'assignee'
        }
        
        for key, value in expected_mappings.items():
            assert rename_map[key] == value
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test type mapping generation")
    def test_type_mapping(self, temp_config_file):
        """Test type mapping generation."""
        mapper = FieldMapper(temp_config_file)
        type_mapping = mapper.get_type_mapping()
        
        assert type_mapping['storypoints'] == 'int64'
        assert type_mapping['assignee'] == 'string'
        assert type_mapping['created'] == 'datetime'
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test field mapping application")
    def test_apply_field_mappings(self, temp_config_file, sample_dataframe, mock_logger):
        """Test applying field mappings to DataFrame."""
        mapper = FieldMapper(temp_config_file)
        result_df = mapper.apply_field_mappings(sample_dataframe, mock_logger)

        # Check that columns were renamed
        assert 'storypoints' in result_df.columns
        assert 'assignee' in result_df.columns
        assert 'customfield_10024' not in result_df.columns
        assert 'assignee.accountId' not in result_df.columns
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test columns by type retrieval")
    def test_get_columns_by_type(self, temp_config_file):
        """Test retrieving columns by target type."""
        mapper = FieldMapper(temp_config_file)
        
        int_columns = mapper.get_columns_by_type('int64')
        string_columns = mapper.get_columns_by_type('string')
        datetime_columns = mapper.get_columns_by_type('datetime')
        
        assert 'storypoints' in int_columns
        assert 'assignee' in string_columns
        assert 'created' in datetime_columns


@allure.feature("DataTypeMapper")
@allure.story("Data Type Conversion")
class TestDataTypeMapper:
    """Test suite for DataTypeMapper class."""
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test pandas dtype conversion")
    def test_get_pandas_dtype(self, mock_logger):
        """Test pandas dtype conversion."""
        assert DataTypeMapper.get_pandas_dtype('int64', mock_logger) == pd.Int64Dtype()
        assert DataTypeMapper.get_pandas_dtype('float64', mock_logger) == pd.Float64Dtype()
        assert DataTypeMapper.get_pandas_dtype('string', mock_logger) == 'string'
        assert DataTypeMapper.get_pandas_dtype('unknown_type', mock_logger) == 'object'
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test safe type conversion")
    def test_safe_type_conversion(self, mock_logger):
        """Test safe type conversion with error handling."""
        df = pd.DataFrame({'test_col': ['1', '2', '3', 'invalid']})

        # Test int64 conversion
        result_df = DataTypeMapper.safe_type_conversion(df, 'test_col', 'int64', mock_logger)

        # Should convert valid values and set invalid to NaN
        assert result_df['test_col'].dtype == pd.Int64Dtype()
        assert pd.isna(result_df['test_col'].iloc[3])  # 'invalid' should be NaN


@allure.feature("RobustDataTypeHandler")
@allure.story("Robust Type Conversion")
class TestRobustDataTypeHandler:
    """Test suite for RobustDataTypeHandler class."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test safe astype conversion")
    def test_safe_astype_conversion(self, mock_logger):
        """Test safe astype conversion with comprehensive error handling."""
        handler = RobustDataTypeHandler()

        df = pd.DataFrame({
            'int_col': ['1', '2', '3', 'invalid'],
            'float_col': ['1.5', '2.5', '3.5', ''],
            'string_col': ['a', 'b', 'c', 'd']
        })

        type_mapping = {
            'int_col': 'Int64',
            'float_col': 'Float64',
            'string_col': 'string'
        }

        result_df, results = handler.safe_astype_conversion(df, type_mapping, mock_logger)

        # Check that conversions were attempted
        assert len(results) == 3

        # Check successful conversions
        successful_results = [r for r in results if r.success]
        assert len(successful_results) >= 2  # At least string and partial numeric conversions
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test field-based type conversion")
    @patch('dags.data_pipeline.data_type_handlers.get_field_mapper')
    def test_apply_field_based_type_conversion(self, mock_get_field_mapper, mock_logger):
        """Test field-based type conversion."""
        # Mock field mapper
        mock_mapper = MagicMock()
        mock_mapper.get_type_mapping.return_value = {'test_col': 'int64'}
        mock_get_field_mapper.return_value = mock_mapper

        handler = RobustDataTypeHandler()
        df = pd.DataFrame({'test_col': ['1', '2', '3']})

        result_df, results = handler.apply_field_based_type_conversion(df, mock_logger)

        # Verify field mapper was called
        mock_mapper.get_type_mapping.assert_called_once()
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test conversion summary")
    def test_get_conversion_summary(self):
        """Test conversion summary generation."""
        handler = RobustDataTypeHandler()
        
        # Add some mock results
        handler.conversion_results = [
            TypeConversionResult(True, 'col1', 'object', 'int64', rows_affected=10),
            TypeConversionResult(False, 'col2', 'object', 'int64', error_message='Error', rows_failed=5),
            TypeConversionResult(True, 'col3', 'object', 'string', rows_affected=8)
        ]
        
        summary = handler.get_conversion_summary()
        
        assert summary['total_conversions'] == 3
        assert summary['successful'] == 2
        assert summary['failed'] == 1
        assert summary['success_rate'] == 2/3


@allure.feature("IssueProcessor")
@allure.story("Issue Data Processing")
class TestIssueProcessor:
    """Test suite for IssueProcessor class."""
    
    @allure.severity(allure.severity_level.CRITICAL)
    @allure.title("Test issue processing with valid data")
    async def test_process_queue_item_valid_data(self, sample_dataframe, mock_logger):
        """Test processing valid issue data."""
        processor = IssueProcessor()
        mock_queue = AsyncMock()

        # Mock the field mapper and type handler
        with patch.object(processor, 'field_mapper') as mock_field_mapper, \
             patch.object(processor, 'type_handler') as mock_type_handler:

            mock_field_mapper.apply_field_mappings.return_value = sample_dataframe
            mock_field_mapper.get_drop_column_prefixes.return_value = []
            mock_field_mapper.get_drop_column_exceptions.return_value = set()
            mock_type_handler.handle_special_conversions.return_value = sample_dataframe
            mock_type_handler.apply_field_based_type_conversion.return_value = (sample_dataframe, [])
            mock_type_handler.get_conversion_summary.return_value = {'total_conversions': 0}

            result = await processor.process_queue_item(sample_dataframe, mock_queue, mock_logger)

            assert result is True
            mock_queue.put.assert_called()
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test processing None item")
    async def test_process_queue_item_none(self, mock_logger):
        """Test processing None item (termination signal)."""
        processor = IssueProcessor()
        mock_queue = AsyncMock()

        result = await processor.process_queue_item(None, mock_queue, mock_logger)

        assert result is False
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test processing empty DataFrame")
    async def test_process_queue_item_empty_dataframe(self, mock_logger):
        """Test processing empty DataFrame."""
        processor = IssueProcessor()
        mock_queue = AsyncMock()
        empty_df = pd.DataFrame()

        result = await processor.process_queue_item(empty_df, mock_queue, mock_logger)

        assert result is True
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test description field handling")
    def test_handle_description_field(self):
        """Test description field reconstruction."""
        processor = IssueProcessor()
        
        df = pd.DataFrame({
            'description.type': ['doc', 'doc', None],
            'description.version': [1, 1, None],
            'description.content': ['content1', 'content2', None]
        })
        
        result_df = processor._handle_description_field(df, MagicMock())
        
        # Check that description was reconstructed for non-null rows
        assert 'description' in result_df.columns
        assert isinstance(result_df['description'].iloc[0], dict)
        assert result_df['description'].iloc[0]['type'] == 'doc'
    
    @allure.severity(allure.severity_level.NORMAL)
    @allure.title("Test timetracking handling")
    def test_handle_timetracking(self):
        """Test timetracking field processing."""
        processor = IssueProcessor()
        
        df = pd.DataFrame({
            'timetracking.timeSpent': ['1h', '2h', None],
            'timetracking.remainingEstimate': ['3h', '4h', None],
            'other_col': ['a', 'b', 'c']
        })
        
        result_df = processor._handle_timetracking(df, MagicMock())
        
        # Check that timetracking column was created
        assert 'timetracking' in result_df.columns
        
        # Check that original timetracking columns were dropped
        timetracking_cols = [col for col in result_df.columns if col.startswith('timetracking.')]
        assert len(timetracking_cols) == 0


if __name__ == "__main__":
    # Run tests with allure reporting
    pytest.main([
        __file__,
        "--alluredir=allure-results",
        "--cov=dags.data_pipeline.field_mappers",
        "--cov=dags.data_pipeline.data_type_handlers", 
        "--cov=dags.data_pipeline.queue_processors",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "-v"
    ])
