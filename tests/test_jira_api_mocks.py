"""
Test module for Jira API functionality using mocks instead of real services.
This demonstrates how to properly test the Jira API integration without
relying on external services like Postgres or the Jira API.
"""
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from unittest import skip

import allure
import pytest
from unittest.mock import MagicMock, patch, AsyncMock, Mock
import dags.data_pipeline.utility_code as uc
from dependency_injector.providers import Object

from dags.data_pipeline.containers import (
    JiraEntryDetailsContainer,
    ApplicationContainer,
    EntryDetails,
    PostgresSessionManager, LoggerContainer, QueueContainer
)


@pytest.fixture
def mock_jira_entry():
    """Mock Jira entry details"""
    entry = MagicMock(spec=EntryDetails)
    entry.username = "<EMAIL>"
    entry.password = "test_password"
    entry.url = "https://example.atlassian.net/"
    entry.custom_properties = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    return entry


@pytest.fixture
def mock_jira_container(mock_jira_entry):
    """Mock Jira container with entry details"""
    container = MagicMock(spec=JiraEntryDetailsContainer)
    container.jira_entry_details.return_value = mock_jira_entry
    return container


@pytest.fixture
def mock_app_container():
    """Mock application container"""
    container = MagicMock(spec=ApplicationContainer)
    container.schema.return_value = "test_schema"
    return container


@pytest.fixture
def mock_session():
    """Mock database session"""
    session = MagicMock()
    session.execute.return_value = [("Issues", "2023-01-01 00:00:00")]
    session.commit = MagicMock()
    return session


@pytest.fixture
def mock_db_manager(mock_session):
    """Mock database session manager"""
    manager = MagicMock(spec=PostgresSessionManager)
    manager.session.return_value.__enter__.return_value = mock_session
    return manager


@pytest.fixture
def mock_http_session():
    """Mock aiohttp ClientSession"""
    session = AsyncMock()

    # Mock response for users endpoint
    mock_users_response = AsyncMock()
    mock_users_response.status = 200
    mock_users_response.json.return_value = [
        {
            "accountId": "user1",
            "displayName": "User One",
            "emailAddress": "<EMAIL>",
            "active": True
        },
        {
            "accountId": "user2",
            "displayName": "User Two",
            "emailAddress": "<EMAIL>",
            "active": True
        }
    ]

    # Mock response for boards endpoint
    mock_boards_response = AsyncMock()
    mock_boards_response.status = 200
    mock_boards_response.json.return_value = {
        "values": [
            {
                "id": 1,
                "name": "Board One",
                "type": "scrum",
                "location": {"projectKey": "TEST"}
            },
            {
                "id": 2,
                "name": "Board Two",
                "type": "kanban",
                "location": {"projectKey": "TEST"}
            }
        ]
    }

    # Configure the session get method to return appropriate responses
    async def mock_get(url, params=None, **kwargs):
        if "/rest/api/3/users" in url:
            return mock_users_response
        elif "/rest/agile/1.0/board/" in url:
            return mock_boards_response
        else:
            # Default response
            mock_response = AsyncMock()
            mock_response.status = 404
            return mock_response

    session.get = mock_get
    return session


@pytest.fixture
def di_containers():
    """Fixture to set up and tear down dependency injection containers"""
    # Create containers
    app_container = ApplicationContainer()
    logger_container = LoggerContainer()
    jira_container = JiraEntryDetailsContainer()
    
    # Create mocks
    mock_logger = MagicMock()
    mock_entry = MagicMock()
    mock_entry.url = "https://example.atlassian.net/"
    mock_entry.custom_properties = {"Authorization": "Bearer testtoken"}
    
    mock_db_session = MagicMock()
    mock_db_session.commit = MagicMock()
    
    mock_db_manager = MagicMock()
    mock_db_manager.session.return_value.__enter__.return_value = mock_db_session
    
    # Setup overrides
    logger_container.logger.override(mock_logger)
    jira_container.jira_entry_details.override(mock_entry)
    app_container.logger_container.override(logger_container)
    app_container.database_rw.override(mock_db_manager)
    app_container.schema.override("public")
    
    # Wire containers
    logger_container.wire(modules=["dags.data_pipeline.utility_code"])
    jira_container.wire(modules=["dags.data_pipeline.utility_code"])
    app_container.wire(modules=["dags.data_pipeline.utility_code"])
    
    yield {
        'app_container': app_container,
        'logger_container': logger_container,
        'jira_container': jira_container,
        'mock_logger': mock_logger,
        'mock_entry': mock_entry,
        'mock_db_session': mock_db_session,
        'mock_db_manager': mock_db_manager
    }
    
    # Cleanup
    app_container.unwire()
    logger_container.unwire()
    jira_container.unwire()
    jira_container.jira_entry_details.reset_override()
    app_container.logger_container.reset_override()
    app_container.database_rw.reset_override()
    app_container.schema.reset_override()


@allure.feature("Jira API")
@allure.story("Get Jira Users")
class TestJiraUsers:
    @patch("aiohttp.ClientSession")
    @patch("dags.data_pipeline.utility_code.fetch_with_retries_get")
    @pytest.mark.asyncio
    @allure.title("Test get_jira_users with mocked services")
    async def test_get_jira_users(self, mock_fetch_function, mock_client_session):
        """Test get_jira_users with properly mocked dependencies using dependency injection"""

        # Setup mock response for fetch function
        mock_fetch_function.return_value = {
            "success": True,
            "result": [
                {
                    "accountId": "user1", 
                    "displayName": "User One", 
                    "emailAddress": "<EMAIL>", 
                    "active": True,
                    "self": "https://example.atlassian.net/rest/api/3/user?accountId=user1",
                    "avatarUrls": {
                        "48x48": "https://example.atlassian.net/secure/useravatar?size=large&avatarId=10120",
                        "24x24": "https://example.atlassian.net/secure/useravatar?size=small&avatarId=10120",
                    }
                }
            ]
        }

        # Create mock logger
        mock_logger = MagicMock()
        mock_logger.info = MagicMock()
        mock_logger.error = MagicMock()
        mock_logger.warning = MagicMock()
        mock_logger.exception = MagicMock()
        mock_logger.debug = MagicMock()

        # Create mock circuit breaker
        mock_circuit_breaker = AsyncMock()
        mock_circuit_breaker.can_execute.return_value = True
        mock_circuit_breaker.enter_request = AsyncMock()
        mock_circuit_breaker.record_success = AsyncMock()
        mock_circuit_breaker.record_failure = AsyncMock()
        mock_circuit_breaker.record_rate_limit = AsyncMock()
        mock_circuit_breaker.wait_for_recovery = AsyncMock()

        # Mock entry details
        mock_entry = MagicMock()
        mock_entry.url = "https://example.atlassian.net/"
        mock_entry.custom_properties = {"Authorization": "Bearer testtoken"}

        # Mock database session
        mock_pg_session = MagicMock()
        mock_pg_session.commit = MagicMock()
        
        # Mock database manager
        mock_db_manager = MagicMock()
        mock_db_manager.session.return_value.__enter__.return_value = mock_pg_session

        # Setup containers with proper dependency injection
        app_container = ApplicationContainer()
        
        # Create logger container
        logger_container = LoggerContainer()
        logger_container.logger.override(mock_logger)
        
        # Create jira container
        jira_container = JiraEntryDetailsContainer()
        
        # Override all providers before wiring
        jira_container.jira_entry_details.override(mock_entry)
        app_container.logger_container.override(logger_container)
        app_container.database_rw.override(mock_db_manager)
        app_container.schema.override("public")
        app_container.circuit_breaker.override(mock_circuit_breaker)

        # Wire the containers
        logger_container.wire(modules=[__name__, "dags.data_pipeline.utility_code"])
        jira_container.wire(modules=[__name__, "dags.data_pipeline.utility_code"])
        app_container.wire(modules=[__name__, "dags.data_pipeline.utility_code"])

        try:
            # Mock the aiohttp session
            mock_session = AsyncMock()
            mock_client_session.return_value.__aenter__.return_value = mock_session

            # Run the function
            result = await uc.get_jira_users()

            # Assertions
            assert result is not None
            assert isinstance(result, str)
            assert result.startswith("+") or result in ["DB EXCEPTION", "UNKNOWN EXCEPTION", "FAILED"]
            
            # Verify mocks were called appropriately
            mock_fetch_function.assert_called()
            mock_logger.debug.assert_called()
            
            print(f"Test completed successfully with result: {result}")

        finally:
            # Clean up overrides to avoid affecting other tests
            app_container.unwire()
            logger_container.unwire()
            jira_container.unwire()
            jira_container.jira_entry_details.reset_override()
            app_container.logger_container.reset_override()
            app_container.database_rw.reset_override()
            app_container.schema.reset_override()
            app_container.circuit_breaker.reset_override()

    @patch("aiohttp.ClientSession")
    @patch("dags.data_pipeline.utility_code.get_issues_from_jira_jql")
    @pytest.mark.asyncio
    @allure.title("Test get_jira_users with complete mocking")
    async def test_get_jira_users_complete_mock(self, mock_get_issues, mock_client_session):
        """Alternative approach - mock the entire function chain"""

        # Setup DI containers
        app_container = ApplicationContainer()
        logger_container = LoggerContainer()
        jira_container = JiraEntryDetailsContainer()

        # Create comprehensive mocks
        mock_logger = MagicMock()
        for method in ['info', 'error', 'warning', 'exception', 'debug']:
            setattr(mock_logger, method, MagicMock())

        mock_circuit_breaker = AsyncMock()
        mock_circuit_breaker.can_execute.return_value = True
        mock_circuit_breaker.enter_request = AsyncMock()
        mock_circuit_breaker.record_success = AsyncMock()

        mock_entry = MagicMock()
        mock_entry.url = "https://example.atlassian.net/"
        mock_entry.custom_properties = {"Authorization": "Bearer testtoken"}

        # Setup container overrides
        logger_container.logger.override(mock_logger)
        jira_container.jira_entry_details.override(mock_entry)
        app_container.logger_container.override(logger_container)
        app_container.circuit_breaker.override(mock_circuit_breaker)

        # Mock the HTTP session to avoid actual network calls
        mock_session = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = []
        mock_session.request.return_value.__aenter__.return_value = mock_response
        mock_client_session.return_value.__aenter__.return_value = mock_session

        # Wire containers
        logger_container.wire(modules=["dags.data_pipeline.utility_code"])
        jira_container.wire(modules=["dags.data_pipeline.utility_code"])
        app_container.wire(modules=["dags.data_pipeline.utility_code"])

        try:
            # Patch the specific function that's causing issues
            with patch('dags.data_pipeline.utility_code.fetch_with_retries_get') as mock_fetch:
                mock_fetch.return_value = {"success": True, "result": []}

                result = await uc.get_jira_users()
                assert result is not None

        finally:
            # Cleanup
            app_container.unwire()
            logger_container.unwire()
            jira_container.unwire()
            jira_container.jira_entry_details.reset_override()
            app_container.logger_container.reset_override()
            app_container.circuit_breaker.reset_override()

    @patch("dags.data_pipeline.utility_code.fetch_with_retries_get")
    @pytest.mark.asyncio
    @allure.title("Test get_jira_users with simplified DI approach")
    async def test_get_jira_users_simplified(self, mock_fetch_function):
        """Simplified test using proper dependency injection pattern"""
        
        # Setup mock response
        mock_fetch_function.return_value = {
            "success": True,
            "result": [
                {
                    "accountId": "user1",
                    "displayName": "User One", 
                    "emailAddress": "<EMAIL>",
                    "active": True,
                    "self": "https://example.atlassian.net/rest/api/3/user?accountId=user1",
                    "avatarUrls": {
                        "48x48": "https://example.atlassian.net/secure/useravatar?size=large&avatarId=10120",
                        "24x24": "https://example.atlassian.net/secure/useravatar?size=small&avatarId=10120",
                    }
                }
            ]
        }

        # Create containers
        app_container = ApplicationContainer()
        logger_container = LoggerContainer()
        jira_container = JiraEntryDetailsContainer()

        # Create mocks
        mock_logger = MagicMock()
        mock_entry = MagicMock()
        mock_entry.url = "https://example.atlassian.net/"
        mock_entry.custom_properties = {"Authorization": "Bearer testtoken"}

        mock_db_session = MagicMock()
        mock_db_session.commit = MagicMock()
        
        mock_db_manager = MagicMock()
        mock_db_manager.session.return_value.__enter__.return_value = mock_db_session

        # Setup overrides
        logger_container.logger.override(mock_logger)
        jira_container.jira_entry_details.override(mock_entry)
        app_container.logger_container.override(logger_container)
        app_container.database_rw.override(mock_db_manager)
        app_container.schema.override("public")

        # Wire containers
        logger_container.wire(modules=["dags.data_pipeline.utility_code"])
        jira_container.wire(modules=["dags.data_pipeline.utility_code"])
        app_container.wire(modules=["dags.data_pipeline.utility_code"])

        try:
            # Execute function
            result = await uc.get_jira_users()
            
            # Assertions
            assert result is not None
            assert isinstance(result, str)
            assert result.startswith("+") or result in ["DB EXCEPTION", "UNKNOWN EXCEPTION", "FAILED"]
            
            # Verify mocks were called
            mock_fetch_function.assert_called()
            mock_db_session.commit.assert_called()
            
        finally:
            # Cleanup
            app_container.unwire()
            logger_container.unwire()
            jira_container.unwire()
            jira_container.jira_entry_details.reset_override()
            app_container.logger_container.reset_override()
            app_container.database_rw.reset_override()
            app_container.schema.reset_override()

    @patch("dags.data_pipeline.utility_code.fetch_with_retries_get")
    @pytest.mark.asyncio
    @allure.title("Test get_jira_users using DI fixture")
    async def test_get_jira_users_with_fixture(self, mock_fetch_function, di_containers):
        """Test using the DI fixture for clean dependency injection setup"""
        
        # Setup mock response
        mock_fetch_function.return_value = {
            "success": True,
            "result": [
                {
                    "accountId": "user1",
                    "displayName": "User One", 
                    "emailAddress": "<EMAIL>",
                    "active": True,
                    "self": "https://example.atlassian.net/rest/api/3/user?accountId=user1",
                    "avatarUrls": {
                        "48x48": "https://example.atlassian.net/secure/useravatar?size=large&avatarId=10120",
                        "24x24": "https://example.atlassian.net/secure/useravatar?size=small&avatarId=10120",
                    }
                }
            ]
        }

        # Execute function
        result = await uc.get_jira_users()
        
        # Assertions
        assert result is not None
        assert isinstance(result, str)
        assert result.startswith("+") or result in ["DB EXCEPTION", "UNKNOWN EXCEPTION", "FAILED"]
        
        # Verify mocks were called
        mock_fetch_function.assert_called()
        di_containers['mock_db_session'].commit.assert_called()
        di_containers['mock_logger'].debug.assert_called()


@allure.feature("Jira API")
@allure.story("Process Jira Issues")
class TestJiraIssues:

    @pytest.mark.asyncio
    @allure.title("Test process_jira_issues with mocked services")
    async def test_process_jira_issues(self, mock_jira_entry, mock_app_container, mock_db_manager):
        """Test process_jira_issues function with mocked external services"""

        # Mock project key and scope
        project_key = "TEST"
        scope = "project"
        initial_load = True

        # Setup DI containers
        app_container = ApplicationContainer()
        logger_container = LoggerContainer()
        jira_container = JiraEntryDetailsContainer()

        q_container = QueueContainer()

        # Create mocks
        mock_logger = MagicMock()
        mock_entry = MagicMock()
        mock_entry.url = "https://example.atlassian.net/"
        mock_entry.custom_properties = {"Authorization": "Bearer testtoken"}


        # Setup overrides
        logger_container.logger.override(mock_logger)
        jira_container.jira_entry_details.override(mock_entry)
        app_container.logger_container.override(logger_container)
        app_container.database_rw.override(mock_db_manager)
        app_container.schema.override("public")

        # Wire containers
        logger_container.wire(modules=["dags.data_pipeline.utility_code"])
        jira_container.wire(modules=["dags.data_pipeline.utility_code"])
        app_container.wire(modules=["dags.data_pipeline.utility_code"])
        q_container.wire(modules=["dags.data_pipeline.utility_code"])

        # Patch the necessary functions and classes
        with (
            patch("dags.data_pipeline.utility_code.get_issues_from_jira_jql") as mock_get_issues,
            patch("aiohttp.ClientSession") as mock_client_session,
            patch("dags.data_pipeline.utility_code.fetch_with_retries_get") as mock_fetch
        ):

            # Configure the mock session
            mock_session = AsyncMock()
            mock_client_session_instance = AsyncMock()
            mock_client_session_instance.__aenter__.return_value = mock_session
            mock_client_session.return_value = mock_client_session_instance

            # Return timezone info from fetch
            mock_fetch.return_value = {'result': {'timeZone': 'Asia/Kolkata'}}
            mock_db_manager.get_last_run_timestamp = AsyncMock(
                return_value=datetime(2024, 1, 1, 12, 0, tzinfo=timezone.utc)
            )

            mock_get_issues.return_value = None

            # Create mock for enhanced DB manager
            mock_db_enhanced = MagicMock()

            # Create mock for the result of update_schema()
            mock_schema_manager = MagicMock()

            # Create mock async session context manager
            mock_async_session_context = AsyncMock()
            mock_async_session = AsyncMock()
            mock_async_session_context.__aenter__.return_value = mock_async_session
            mock_async_session_context.__aexit__.return_value = None

            # Wire the chain:
            # database_rw_enhanced().update_schema(project_key).async_session()
            mock_schema_manager.async_session.return_value = mock_async_session_context
            mock_db_enhanced.update_schema.return_value = mock_schema_manager

            # Apply override
            app_container.database_rw_enhanced.override(mock_db_enhanced)

            # Simulated return from execute()
            mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = datetime(2023, 12, 31, 10, 0, tzinfo=timezone.utc)

            # Assign it to async mock
            mock_async_session.execute.return_value = mock_result

            try:
                # Call the function with our mocked dependencies
                result = await uc.process_jira_issues(
                    project_key=project_key,
                    scope=scope,
                    initial_load=initial_load,
                    jira_entry=mock_entry,
                    app_container=app_container,

                )

                mock_fetch.assert_awaited_once()
                mock_get_issues.assert_called()

                # Verify the result
                assert result is None or isinstance(result, str), "Function should return None or a string"
            #
            # except AttributeError as e:
            #     # If the function signature is different, this test needs to be updated
            #     pytest.skip("Function signature has changed, test needs updating")
            finally:
                # Cleanup
                app_container.unwire()
                logger_container.unwire()
                jira_container.unwire()
                jira_container.jira_entry_details.reset_override()
                app_container.logger_container.reset_override()
                app_container.database_rw.reset_override()
                app_container.schema.reset_override()
