# coding=utf-8
import allure
import pytest

from psycopg2.errors import DeadlockDetected
from asyncpg.exceptions import DeadlockDetectedError

from dags.data_pipeline import utility_code


@utility_code.smart_retry(min_wait=0.1, max_wait=0.2, max_retries=3)
def sync_func():
    raise DeadlockDetected("Simulated sync deadlock")


@pytest.mark.suite_smart_retry
@allure.title("Test sync function success after retries")
@allure.description("Tests that the sync function successfully completes after retrying due to deadlocks.")
def test_sync_success():
    call_count = {"count": 0}

    @utility_code.smart_retry(min_wait=0.1, max_wait=0.2, max_retries=3)
    def sync_function():
        call_count["count"] += 1
        if call_count["count"] == 3:
            return "Success"
        raise DeadlockDetected("Simulated sync deadlock")

    result = sync_function()
    assert result == "Success"
    assert call_count["count"] == 3
    allure.attach(
        f"Total attempts: {call_count['count']}", name="Sync Retry Attempts",
        attachment_type=allure.attachment_type.TEXT
    )


@pytest.mark.suite_smart_retry
@allure.title("Test sync function max retries limit")
@allure.description("Tests that the sync function raises TimeoutError after reaching the max retries limit.")
def test_sync_max_retries():
    # Test that the function raises TimeoutError after max retries
    with pytest.raises(TimeoutError):
        sync_func()


@utility_code.smart_retry(min_wait=0.1, max_wait=0.2, max_retries=3)
async def async_func():
    raise DeadlockDetectedError("Simulated async deadlock")


@pytest.mark.suite_smart_retry
@allure.title("Test async function success after retries")
@allure.description("Tests that the async function successfully completes after retrying due to deadlocks.")
@pytest.mark.asyncio
async def test_async_success():
    call_count = {"count": 0}

    @utility_code.smart_retry(min_wait=0.1, max_wait=0.2, max_retries=3)
    async def async_function():
        call_count["count"] += 1
        if call_count["count"] == 3:
            return "Success"
        raise DeadlockDetectedError("Simulated async deadlock")

    result = await async_function()
    assert result == "Success"
    assert call_count["count"] == 3
    allure.attach(
        f"Total attempts: {call_count['count']}", name="Async Retry Attempts",
        attachment_type=allure.attachment_type.TEXT
    )


@pytest.mark.suite_smart_retry
@allure.title("Test async function max retries limit")
@allure.description("Tests that the async function raises TimeoutError after reaching the max retries limit.")
@pytest.mark.asyncio
async def test_async_max_retries():
    # Test that the async function raises TimeoutError after max retries
    with pytest.raises(TimeoutError):
        await async_func()

@pytest.mark.suite_smart_retry
@allure.title("Test invalid retries configuration")
@allure.description("Tests that the function raises ValueError for invalid retry configurations.")
def test_invalid_retries():
    with pytest.raises(ValueError, match="Wait times and max_delay must be positive numbers"):
        @utility_code.smart_retry(min_wait=-1)  # Invalid configuration
        def invalid_func():
            pass

        invalid_func()

    with pytest.raises(ValueError, match="min_wait cannot be greater than max_wait"):
        @utility_code.smart_retry(min_wait=10, max_wait=5)  # Invalid configuration
        def invalid_func():
            pass

        invalid_func()

    with pytest.raises(ValueError, match="max_retries must be a positive integer"):
        @utility_code.smart_retry(max_retries=0)  # Invalid configuration
        def invalid_func():
            pass

        invalid_func()
    allure.attach(
        "Test invalid retry configurations completed", name="Invalid Retries Test",
        attachment_type=allure.attachment_type.TEXT
    )
