{"name": "test_successful_fetch_returns_result", "status": "broken", "statusDetails": {"message": "KeyError: 'result'", "trace": "fixturefunc = <function assert_success at 0x000001E303A4EFC0>\nrequest = <FixtureRequest for <Function test_successful_fetch_returns_result>>\nkwargs = {'context': {}}\n\n    def call_fixture_func(\n        fixturefunc: _FixtureFunc[FixtureValue], request: FixtureRequest, kwargs\n    ) -> FixtureValue:\n        if inspect.isgeneratorfunction(fixturefunc):\n            fixturefunc = cast(Callable[..., Generator[FixtureValue]], fixturefunc)\n            generator = fixturefunc(**kwargs)\n            try:\n                fixture_result = next(generator)\n            except StopIteration:\n                raise ValueError(f\"{request.fixturename} did not yield a value\") from None\n            finalizer = functools.partial(_teardown_yield_fixture, fixturefunc, generator)\n            request.addfinalizer(finalizer)\n        else:\n            fixturefunc = cast(Callable[..., FixtureValue], fixturefunc)\n>           fixture_result = fixturefunc(**kwargs)\n                             ^^^^^^^^^^^^^^^^^^^^^\n\n..\\..\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py:929: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\ncontext = {}\n\n    @then(\"the result should indicate success\")\n    def assert_success(context):\n>       assert context[\"result\"][\"success\"] is True\n               ^^^^^^^^^^^^^^^^^\nE       KeyError: 'result'\n\ntest_fetch_steps.py:39: KeyError"}, "description": "C:\\Users\\<USER>\\PycharmProjects\\airflow\\tests\\features\\fetch_with_retries.feature: Successful fetch returns result", "start": 1751040869006, "stop": 1751040869010, "uuid": "732ff4bb-24fa-4934-87ee-554dddad0537", "historyId": "30881ec731ea11d5f28f7606404380db", "testCaseId": "30881ec731ea11d5f28f7606404380db", "fullName": "tests.steps.test_fetch_steps#test_successful_fetch_returns_result", "labels": [{"name": "parentSuite", "value": "tests.steps"}, {"name": "suite", "value": "test_fetch_steps"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "28440-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.steps.test_fetch_steps"}]}