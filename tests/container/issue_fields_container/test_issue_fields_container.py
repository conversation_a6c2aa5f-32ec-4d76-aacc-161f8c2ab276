import pytest
import allure

from dags.data_pipeline.containers import IssueFieldsContainer


@pytest.fixture(scope="module")
@allure.title("Set up IssueFieldsContainer")
def container():
    """Set up the dependency injection container."""
    container = IssueFieldsContainer()
    yield container


@allure.feature("FieldNameExtractor")
class TestFieldNameExtractor:

    @allure.story("Extract field names")
    @allure.title("Verify all field names are extracted correctly")
    def test_get_field_names(self, container):
        with allure.step("Resolve FieldNameExtractor from the container"):
            extractor = container.field_name_extractor()
        with allure.step("Extract field names"):
            field_names = extractor.get_field_names()
        with allure.step("Verify the field names match the expected values"):
            assert len(field_names) > 0, "No field names were extracted"
            assert "aggregateprogress" in field_names, "Field ID 'aggregateprogress' is missing"

    @allure.story("Extract field IDs by datatype")
    @allure.title("Verify field IDs for a specific datatype")
    @pytest.mark.parametrize("datatype,expected_ids", [
        ("progress", ["aggregateprogress", "progress"]),
        ("number", [
            'aggregatetimeestimate', 'aggregatetimeoriginalestimate', 'aggregatetimespent',
            'customfield_10024', 'customfield_10120', 'customfield_10121',
            'customfield_10122', 'customfield_10123', 'customfield_10124',
            'customfield_10125', 'customfield_10126', 'customfield_10147', 'customfield_10199',
            'timeestimate', 'timeoriginalestimate', 'timespent'
        ]),
        ("user", ["assignee", "reporter"]),
        ("datetime", ["created", "resolutiondate", "updated", "statuscategorychangedate"]),
        ("nonexistent", []),  # Test for no matches
    ])
    def test_get_field_ids_by_datatype(self, container, datatype, expected_ids):
        with allure.step("Resolve FieldNameExtractor from the container"):
            extractor = container.field_name_extractor()
        with allure.step(f"Extract field IDs for datatype '{datatype}'"):
            result = extractor.get_field_ids_by_datatype(datatype)
        with allure.step("Verify the field IDs match the expected values"):
            assert sorted(result) == sorted(expected_ids), f"Field IDs for datatype '{datatype}' do not match"
