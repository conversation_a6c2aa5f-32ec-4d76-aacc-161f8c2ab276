#!/usr/bin/env python3
"""
Test script to verify fetch_with_retries consistency fixes.
This script tests the return format consistency and proper error handling.
"""

import asyncio
from pathlib import Path
from types import ModuleType
from typing import Optional

import aiohttp
from unittest.mock import AsyncMock, MagicMock
import sys
import os

import pytest

import dags.data_pipeline.jira.api_client

project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

# Add the project root to the path so we can import the modules
# sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Mock the dependencies that might not be available in test environment
# 🧪 Patch custom_logger before importing utility_code
mock_logger_module = ModuleType("custom_logger")

class MockFormatter:
    def __init__(self, *args, **kwargs): pass
    def format(self, record): return str(record.msg)

mock_logger_module.CustomFormatter = MockFormatter

# Inject mock module into sys.modules
sys.modules["custom_logger"] = mock_logger_module

class MockLogger:
    def debug(self, msg): print(f"DEBUG: {msg}")
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")

class MockCircuitBreaker:
    async def record_success(self): pass
    async def record_failure(self): pass
    async def wait_for_recovery(self, timeout: Optional[float] = None): pass

    async def can_execute(self): return True
    async def enter_request(self): pass
    async def exit_request(self): pass
    async def is_open(self): pass

    state = "CLOSED"

# Mock the dependency injection
def mock_inject(func):
    return func

def mock_debug_async_function(name):
    def decorator(func):
        return func
    return decorator

# Patch the imports
import dags.data_pipeline.utility_code as uc
uc.inject = mock_inject
uc.debug_async_function = mock_debug_async_function

@pytest.mark.asyncio
async def test_fetch_with_retries_success():
    """Test that successful responses have consistent format"""
    print("\n=== Testing Successful Response Format ===")
    
    # Mock a successful HTTP response
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.json = AsyncMock(return_value={"data": "test"})
    mock_response.headers = {}
    

    mock_request_ctx_mgr = AsyncMock()
    mock_request_ctx_mgr.__aenter__.return_value = mock_response
    mock_request_ctx_mgr.__aexit__.return_value = None

    mock_session = AsyncMock(spec=aiohttp.ClientSession)
    mock_session.request.return_value = mock_request_ctx_mgr

    # mock_session = AsyncMock()
    # mock_session.request.return_value.__aenter__.return_value = mock_response
    
    # Test the function
    result = await dags.data_pipeline.jira.api_client.fetch_with_retries(
        session=mock_session,
        method="GET",
        url="http://test.com",
        my_logger=MockLogger(),
        global_circuit_breaker=MockCircuitBreaker()
    )

    # Verify the format
    assert isinstance(result, dict), "Result should be a dictionary"
    assert "success" in result, "Result should have 'success' key"
    assert "result" in result, "Result should have 'result' key"
    assert result["success"] is True, "Success should be True"
    assert result["result"] == {"data": "test"}, "Result should contain the response data"
    assert "exception" not in result, "Successful response should not have 'exception' key"
    
    print("✓ Successful response format is correct")

@pytest.mark.asyncio
async def test_fetch_with_retries_failure():
    """Test that failed responses have consistent format"""
    print("\n=== Testing Failed Response Format ===")
    
    # Mock a failed HTTP response
    mock_response = AsyncMock()
    mock_response.status = 500
    mock_response.url = "http://test.com"

    mock_request_ctx_mgr = AsyncMock()
    mock_request_ctx_mgr.__aenter__.return_value = mock_response
    mock_request_ctx_mgr.__aexit__.return_value = None

    mock_session = AsyncMock(spec=aiohttp.ClientSession)
    mock_session.request.return_value = mock_request_ctx_mgr
    
    # mock_session = AsyncMock()
    # mock_session.request.return_value.__aenter__.return_value = mock_response
    
    # Test the function
    result = await dags.data_pipeline.jira.api_client.fetch_with_retries(
        session=mock_session,
        method="GET",
        url="http://test.com",
        retries=0,  # Don't retry for this test
        my_logger=MockLogger(),
        global_circuit_breaker=MockCircuitBreaker()
    )

    # Verify the format
    assert isinstance(result, dict), "Result should be a dictionary"
    assert "success" in result, "Result should have 'success' key"
    assert "exception" in result, "Failed result should have 'exception' key"
    assert result["success"] is False, "Success should be False"
    assert "HTTP 500" in str(result["exception"]), "Exception should contain HTTP status"
    
    print("✓ Failed response format is correct")

def test_safe_response_access_patterns():
    """Test that our fixed code patterns safely access response data"""
    print("\n=== Testing Safe Response Access Patterns ===")
    
    # Test successful response access
    success_response = {"success": True, "result": {"data": "test"}}
    
    # This should work safely
    if success_response.get("success"):
        result = success_response.get("result", {})
        data = result.get("data")
        assert data == "test", "Should safely access nested data"
    
    # Test failed response access
    failed_response = {"success": False, "exception": "HTTP 500"}
    
    # This should work safely
    if not failed_response.get("success"):
        exception = failed_response.get("exception")
        assert exception == "HTTP 500", "Should safely access exception"
    
    # Test missing keys (shouldn't crash)
    incomplete_response = {"success": True}  # Missing result key
    
    if incomplete_response.get("success"):
        result = incomplete_response.get("result", {})  # Should default to {}
        assert result == {}, "Should handle missing result key safely"
    
    print("✓ Safe response access patterns work correctly")

def test_response_format_consistency():
    """Test that all response formats are consistent"""
    print("\n=== Testing Response Format Consistency ===")
    
    # All responses should have these characteristics:
    test_responses = [
        {"success": True, "result": {"data": "test"}},
        {"success": False, "exception": "Error message"},
        {"success": True, "result": None},  # 204 No Content case
    ]
    
    for i, response in enumerate(test_responses):
        assert isinstance(response, dict), f"Response {i} should be a dict"
        assert "success" in response, f"Response {i} should have 'success' key"
        assert isinstance(response["success"], bool), f"Response {i} 'success' should be boolean"
        
        if response["success"]:
            assert "result" in response, f"Successful response {i} should have 'result' key"
            assert "exception" not in response, f"Successful response {i} should not have 'exception' key"
        else:
            assert "exception" in response, f"Failed response {i} should have 'exception' key"
    
    print("✓ All response formats are consistent")

async def main():
    """Run all tests"""
    print("Testing fetch_with_retries consistency fixes...")
    
    try:
        await test_fetch_with_retries_success()
        await test_fetch_with_retries_failure()
        test_safe_response_access_patterns()
        test_response_format_consistency()
        
        print("\n🎉 All tests passed! The fetch_with_retries function now has consistent return format.")
        print("\nKey improvements made:")
        print("1. ✓ fetch_with_retries always returns {'success': bool, 'result': Any, 'exception': str}")
        print("2. ✓ All callers now check 'success' before accessing 'result'")
        print("3. ✓ Proper error handling for failed requests")
        print("4. ✓ Type hints and documentation updated")
        print("5. ✓ Safe access patterns implemented throughout codebase")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
