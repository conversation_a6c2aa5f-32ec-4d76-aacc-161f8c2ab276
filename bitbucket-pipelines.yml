image: python:3.12

definitions:
  caches:
    pip: ~/.cache/pip
  services:
    postgres:
      image: postgres:16
      memory: 2048
      environment:
        POSTGRES_DB: jira
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres
        DATABASE_URL: postgresql+psycopg2://postgres:postgres@locahost:5432/jira
      volumes:
        - ./init-user-db.sh:/docker-entrypoint-initdb.d/init-user-db.sh
    redis:
      image: redis:alpine
  steps:
    - step: &decrypt
        name: Decrypt the keepass DB
        runs-on:
          - self.hosted
          - linux
        script:
          - echo "$PRIVATE_KEY_BASE64" | base64 --decode > private_key.asc
          - gpg --batch --verbose --passphrase ${GPG_PASSPHRASE} --import private_key.asc
          - gpg --list-secret-keys
          - echo "${GPG_PASSPHRASE}" | gpg --batch --yes --passphrase-fd 0 --pinentry-mode loopback --output Database.kdbx --decrypt Database.kdbx.gpg
          - echo "$GPG_PASSPHRASE" | gpg --batch --yes --pinentry-mode loopback --passphrase-fd 0 --output Database.key -d Database.key.gpg
          - chmod 600 Database.kdbx Database.key
          - echo $(pwd)
          - ls -l $(pwd)
        artifacts:
          - Database.kdbx
          - Database.key
    - step: &install_deps_and_test
        name: Install dependencies and run pytest
        runs-on:
          - self.hosted
          - linux
        services:
          - postgres
        caches:
          - pip
        script:
          - ls -l $(pwd)
          - export AIRFLOW_HOME=$(pwd)
          - export LOGGING_CONFIG_PATH=$(pwd)/data_pipeline/logging_config.yaml
          - export ISSUE_FIELDS_YAML_FILE=$(pwd)/data_pipeline/issue_fields.yaml
          - export PYTHONPATH=$PYTHONPATH:$(pwd):$(pwd)/data_pipeline:$(pwd)/data_pipeline/utilities
          - echo ${LOGGING_CONFIG_PATH}
          - echo $AIRFLOW_HOME
          - echo ${ISSUE_FIELDS_YAML_FILE}
          - echo $PYTHONPATH
          - pip install --upgrade pip
          - pip install -r requirements_dev.txt
          - apt-get update && apt-get install -y postgresql-client
          - python -c "from data_pipeline.utility_code import create_db_extension; create_db_extension();"          -

      artifacts:
        - allure_results

    - step: &generate_report
        name: Generate Allure Report
        runs-on:
          - self.hosted
          - linux
        script:
          - apt-get update && apt-get install -y software-properties-common wget unzip default-jdk
          - wget -qO- https://github.com/allure-framework/allure2/releases/download/2.32.0/allure-2.32.0.tgz | tar -xvz -C /opt/
          - export PATH=$PATH:/opt/allure-2.32.0/bin
          - allure generate allure_results -o allure_report
        artifacts:
          - allure_report
        after-script:
          - rm -f private_key.asc
          - rm -f /opt/atlassian/pipelines/agent/build/Database.kdbx
          - rm -f /opt/atlassian/pipelines/agent/build/Database.key
          - rm -f private.* public.*

pipelines:
  default:
    - step: *decrypt
    - step: *install_deps_and_test
    - parallel:
        # this option allows a force stop on all running steps if any step fails
        fail-fast: true
        steps:
          - step:
              name: Create plat schema
              script:
                - python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('plat');"
          - step:
              name: Create acq schema
              script:
                - python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('acq');"
          - step:
              name: Create plp schema
              script:
                - python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('plp');"
          - step:
              # option can be disabled for a step
              # and its failure won't stop other steps in a group
              fail-fast: false
              name: Create train schema
              script:
                - python -c "from data_pipeline.utility_code import create_schema_tables_ddl; create_schema_tables_ddl('train');"
    - step: *generate_report


