{"name": "Test with_database_cleanup decorator for sync functions", "status": "passed", "description": "Test with_database_cleanup decorator for synchronous functions.", "start": 1751105429825, "stop": 1751105429826, "uuid": "bf2dc2f5-dd9d-447c-9369-2e30229f1fac", "historyId": "f6f059c363048e32c0d008aa4ec22c22", "testCaseId": "f6f059c363048e32c0d008aa4ec22c22", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_with_database_cleanup_decorator_sync", "labels": [{"name": "story", "value": "Helper Functions and Decorators"}, {"name": "feature", "value": "Utility Functions"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}