{"name": "Test pg_rw_entry resolution", "status": "passed", "description": "Test that `pg_rw_entry` resolves correctly.", "start": 1751105531101, "stop": 1751105531101, "uuid": "3b622cba-5024-4ac5-bab7-93d6035aaa36", "historyId": "77b902432dae5417ef6ad6b52bfc1441", "testCaseId": "77b902432dae5417ef6ad6b52bfc1441", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_pg_rw_entry", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}