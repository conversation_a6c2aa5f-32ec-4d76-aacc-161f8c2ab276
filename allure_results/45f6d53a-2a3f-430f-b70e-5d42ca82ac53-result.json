{"name": "Test database_ro session manager resolution", "status": "passed", "description": "Test that `database_ro` resolves correctly.", "attachments": [{"name": "stdout", "source": "589728f8-684f-4e9b-a0a1-0b05e8461a64-attachment.txt", "type": "text/plain"}], "start": 1751105531008, "stop": 1751105531010, "uuid": "38be05b7-c83d-406c-ad65-76b258283747", "historyId": "3c40acb320722585ee2023d2b5390a1c", "testCaseId": "3c40acb320722585ee2023d2b5390a1c", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_database_ro", "labels": [{"name": "severity", "value": "minor"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}