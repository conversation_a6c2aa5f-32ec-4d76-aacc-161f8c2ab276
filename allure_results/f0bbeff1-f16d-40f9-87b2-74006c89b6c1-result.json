{"name": "Test database_rw session manager resolution", "status": "passed", "description": "Test that `database_rw` resolves correctly.", "start": 1751105409572, "stop": 1751105409574, "uuid": "f8b13a75-4f01-424b-8854-ff29cf8677b0", "historyId": "81d802e095fc0799cd09239d0c80c39b", "testCaseId": "81d802e095fc0799cd09239d0c80c39b", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_database_rw", "labels": [{"name": "severity", "value": "minor"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}