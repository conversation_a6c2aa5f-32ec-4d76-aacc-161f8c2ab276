{"name": "Upsert Test for Issue Classification", "status": "passed", "description": "Test the upsert functionality considering null and non-null values in conflict conditions.", "steps": [{"name": "Iterate over test data and perform upserts", "status": "passed", "start": 1751105436101, "stop": 1751105436107}], "attachments": [{"name": "stdout", "source": "dffcdbf7-d211-47a9-a742-545e3c8ea037-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10352, 'TRAIN-6', None, 10347, 10349, 10352, 'TRAIN-16']]"}, {"name": "expected_updated", "value": "['TRAIN-16']"}, {"name": "should_update", "value": "[True]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BooleanClauseList object at 0x00000193F9456B40>"}], "start": 1751105436101, "stop": 1751105436107, "uuid": "455878ae-4523-475c-a977-de2b7932f06e", "historyId": "14fecd5a46ad6be1fdfe0d6da6ebf2c6", "testCaseId": "079f09dafc6eae06e1ec1c4ee0c1442a", "fullName": "tests.upsert_conflict_condition.test_upsert_issue_classification#test_upsert_with_conflict_condition_issue_classification", "labels": [{"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_issue_classification"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_issue_classification"}]}