{"name": "Test pg_rw_entry resolution", "status": "passed", "description": "Test that `pg_rw_entry` resolves correctly.", "start": 1751105409680, "stop": 1751105411371, "uuid": "56bb4d3b-**************-646b81cd1cec", "historyId": "e0b66914c26711b12e1f363b6de80dd5", "testCaseId": "e0b66914c26711b12e1f363b6de80dd5", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_rw_entry", "labels": [{"name": "requirement", "value": "AIR-1"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test against real db"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}], "links": [{"type": "issue", "url": "AIR-41", "name": "View issue in Jira"}]}