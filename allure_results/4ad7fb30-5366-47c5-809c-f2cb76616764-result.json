{"name": "Test SQLAlchemy engine creation with mocked psycopg2", "status": "passed", "description": "Test SQLAlchemy engine creation with psycopg2 (mocked).", "attachments": [{"name": "stdout", "source": "3f23282d-2af5-4625-bf3a-2b06a244e1b2-attachment.txt", "type": "text/plain"}], "start": 1751105549596, "stop": 1751105549599, "uuid": "323f585b-06d7-4276-bb17-fcee3166cb96", "historyId": "33005b3b45e1e472468419dae881ce48", "testCaseId": "33005b3b45e1e472468419dae881ce48", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_engine_creation_psycopg2", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}