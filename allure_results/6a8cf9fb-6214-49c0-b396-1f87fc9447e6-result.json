{"name": "test_response_format_consistency", "status": "passed", "description": "Test that all response formats are consistent", "attachments": [{"name": "stdout", "source": "d5bef2e0-de7f-43b3-bb25-0fff212aee23-attachment.txt", "type": "text/plain"}], "start": 1751105432122, "stop": 1751105432123, "uuid": "453d11f3-317b-473e-b0e2-bc58c3077448", "historyId": "ec011eabf47961168b72967409d1dca6", "testCaseId": "ec011eabf47961168b72967409d1dca6", "fullName": "tests.test_fetch_with_retries_consistency#test_response_format_consistency", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}