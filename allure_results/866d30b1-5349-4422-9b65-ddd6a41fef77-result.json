{"name": "Test SQLAlchemy URL creation with psycopg2", "status": "passed", "description": "Test SQLAlchemy URL creation with psycopg2 driver.", "attachments": [{"name": "stdout", "source": "0991d2cc-96b4-4c52-a71e-03109c2307fb-attachment.txt", "type": "text/plain"}], "start": 1751105549575, "stop": 1751105549577, "uuid": "d8907704-4199-4aa7-8754-a7b6ce9a4fad", "historyId": "bcce6ca9b61a783705914930f8b24c03", "testCaseId": "bcce6ca9b61a783705914930f8b24c03", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_url_psycopg2", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Database Drivers"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}