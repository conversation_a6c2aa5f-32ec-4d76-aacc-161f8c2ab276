{"name": "Test get_jira_users with simplified DI approach", "status": "passed", "description": "Simplified test using proper dependency injection pattern", "attachments": [{"name": "stdout", "source": "65990eac-159a-41ff-81d8-841576e97809-attachment.txt", "type": "text/plain"}], "start": 1751105559816, "stop": 1751105562117, "uuid": "8c99daf5-da40-472d-af00-cbed9cbb2a30", "historyId": "abd1892a59abff8c04642f5eaf2d2e70", "testCaseId": "abd1892a59abff8c04642f5eaf2d2e70", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_simplified", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}