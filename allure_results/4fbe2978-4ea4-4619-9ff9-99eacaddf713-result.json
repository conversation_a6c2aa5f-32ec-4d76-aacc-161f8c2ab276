{"name": "Test invalid retries configuration", "status": "passed", "description": "Tests that the function raises ValueError for invalid retry configurations.", "attachments": [{"name": "Invalid Retries Test", "source": "ddc41ec3-a8e4-4710-829b-7e0cd5038c9b-attachment.txt", "type": "text/plain"}], "start": 1751105551735, "stop": 1751105551739, "uuid": "683c4265-ae51-4a42-86b7-bbf79ed5f896", "historyId": "4c941c6537b3e952e6e9cc84b1f8bf4f", "testCaseId": "4c941c6537b3e952e6e9cc84b1f8bf4f", "fullName": "tests.smart_retry.test_smart_retry#test_invalid_retries", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}