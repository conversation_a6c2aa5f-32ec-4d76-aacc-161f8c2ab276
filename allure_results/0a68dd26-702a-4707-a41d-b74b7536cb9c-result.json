{"name": "Test dataclass creation", "status": "passed", "description": "Test that dataclasses work correctly.", "attachments": [{"name": "stdout", "source": "68b5bfca-06ca-472d-909d-560048935bff-attachment.txt", "type": "text/plain"}], "start": 1751105548210, "stop": 1751105548213, "uuid": "28d12fba-8b6d-43b9-acc7-aaeb44d4da7d", "historyId": "df7a5bfac4fe527d868c9ceee9c21b78", "testCaseId": "df7a5bfac4fe527d868c9ceee9c21b78", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_dataclass_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}