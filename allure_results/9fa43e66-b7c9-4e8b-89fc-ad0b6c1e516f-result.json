{"name": "Test pg_rw_entry resolution", "status": "passed", "description": "Test that `pg_rw_entry` resolves correctly.", "start": 1751105409430, "stop": 1751105409431, "uuid": "16d428c9-6ded-46ad-af5e-178b6161ffbd", "historyId": "70158ed251a6a406d2b5125f4d666a99", "testCaseId": "70158ed251a6a406d2b5125f4d666a99", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_pg_rw_entry", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}