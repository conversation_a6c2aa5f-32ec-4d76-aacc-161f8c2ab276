{"name": "Test sync function success after retries", "status": "passed", "description": "Tests that the sync function successfully completes after retrying due to deadlocks.", "attachments": [{"name": "Sync Retry Attempts", "source": "9f5549b3-30d8-4d64-810f-0a25ab50144f-attachment.txt", "type": "text/plain"}], "start": 1751105549636, "stop": 1751105550041, "uuid": "1a37c339-faa3-4dbb-beaa-aeb514ef0f99", "historyId": "d4e645d86e3313fc57a0afc12ceb0a92", "testCaseId": "d4e645d86e3313fc57a0afc12ceb0a92", "fullName": "tests.smart_retry.test_smart_retry#test_sync_success", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}