{"name": "Test get_kp_entry_details resolution for JIRA_ENTRY", "status": "passed", "description": "Test that the `jira_entry_details` resolves correctly with the appropriate title.", "start": 1751105428995, "stop": 1751105428997, "uuid": "87857474-39dd-4838-8080-5a388e85344e", "historyId": "84d86013e4943561b2b41faa63cb8e56", "testCaseId": "84d86013e4943561b2b41faa63cb8e56", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_jira_entry_details", "labels": [{"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}