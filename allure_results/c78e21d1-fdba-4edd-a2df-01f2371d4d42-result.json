{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751105425561, "stop": 1751105425561}, {"name": "Extract field IDs for datatype 'datetime'", "status": "passed", "start": 1751105425561, "stop": 1751105425561}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751105425561, "stop": 1751105425561}], "parameters": [{"name": "datatype", "value": "'datetime'"}, {"name": "expected_ids", "value": "['created', 'resolutiondate', 'updated', 'statuscategorychangedate']"}], "start": 1751105425560, "stop": 1751105425562, "uuid": "0fb3251f-d627-4a97-b68b-bd45f8776cfb", "historyId": "32a2b57aabe04c121de3a2b6939bbf5b", "testCaseId": "77a2f6d4d2ec5a0725fb029808f275a1", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}