{"name": "Test async function max retries limit", "status": "passed", "description": "Tests that the async function raises TimeoutError after reaching the max retries limit.", "start": 1751105551103, "stop": 1751105551724, "uuid": "1a72c2a9-e737-472c-81a2-18d8c80fe8ad", "historyId": "7a3c69592401c9dd4742d6407880e28f", "testCaseId": "7a3c69592401c9dd4742d6407880e28f", "fullName": "tests.smart_retry.test_smart_retry#test_async_max_retries", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}