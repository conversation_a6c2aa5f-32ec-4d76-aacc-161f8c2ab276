{"name": "Test SQLAlchemy URL creation", "status": "passed", "description": "Test SQLAlchemy URL creation with correct drivers.", "attachments": [{"name": "stdout", "source": "90eccd4b-6024-45fd-a666-b7b9511b7d6c-attachment.txt", "type": "text/plain"}], "start": 1751105548182, "stop": 1751105548184, "uuid": "7d6b94db-bd71-4e8d-9b1e-b9ef4610da5b", "historyId": "53cdb10bcbd149f145c7d41d48419943", "testCaseId": "53cdb10bcbd149f145c7d41d48419943", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_sqlalchemy_url_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}