{"name": "test_psycopg3_conflict", "status": "passed", "description": "Test if psycopg3 is installed (should not be).", "attachments": [{"name": "stdout", "source": "5a038914-1f79-4cff-904d-d070bae4d330-attachment.txt", "type": "text/plain"}], "start": 1751105548149, "stop": 1751105548152, "uuid": "73851df4-9d66-4ddb-b330-ecbb643851aa", "historyId": "ba4f6d37eb420a3ca20b332c62b9a5e6", "testCaseId": "ba4f6d37eb420a3ca20b332c62b9a5e6", "fullName": "tests.container.quick_test#test_psycopg3_conflict", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "quick_test"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.quick_test"}]}