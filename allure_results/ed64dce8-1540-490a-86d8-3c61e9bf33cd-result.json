{"name": "test_url_creation", "status": "passed", "description": "Test SQLAlchemy URL creation.", "attachments": [{"name": "stdout", "source": "4a758de7-4ff5-485a-9e23-f8b5a8fac494-attachment.txt", "type": "text/plain"}], "start": 1751105548136, "stop": 1751105548138, "uuid": "a4d1e24b-4b94-408c-9f1a-20d35251e984", "historyId": "899ddf681adcb4365688f9e4ed326419", "testCaseId": "899ddf681adcb4365688f9e4ed326419", "fullName": "tests.container.quick_test#test_url_creation", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "quick_test"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.quick_test"}]}