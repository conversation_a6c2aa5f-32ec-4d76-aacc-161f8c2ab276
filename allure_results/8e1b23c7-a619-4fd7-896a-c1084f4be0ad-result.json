{"name": "Test SQLAlchemy URL creation", "status": "passed", "description": "Test SQLAlchemy URL creation with correct drivers.", "attachments": [{"name": "stdout", "source": "97fe3741-b858-4e0c-8a2d-313ef0653834-attachment.txt", "type": "text/plain"}], "start": 1751105429159, "stop": 1751105429160, "uuid": "a84ca990-6f49-4926-93d2-e7c30d1e398a", "historyId": "53cdb10bcbd149f145c7d41d48419943", "testCaseId": "53cdb10bcbd149f145c7d41d48419943", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_sqlalchemy_url_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}