{"name": "Test pg_ro_entry resolution with different schema", "status": "skipped", "statusDetails": {"message": "Skipped: Test is a work in progress", "trace": "('C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\airflow\\\\tests\\\\container\\\\application_container\\\\test_application_container_experiment.py', 214, 'Skipped: Test is a work in progress')"}, "description": "Test that `pg_ro_entry` resolves with a different schema.", "start": 1751105531023, "stop": 1751105531023, "uuid": "fb0d036d-f8ee-434f-b79a-d6d3d7c0ec77", "historyId": "30648c869b4f56051aae22a4c4e12080", "testCaseId": "30648c869b4f56051aae22a4c4e12080", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_pg_ro_entry_different_schema", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}