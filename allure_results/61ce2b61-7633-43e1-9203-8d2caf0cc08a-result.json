{"name": "Test with_database_cleanup decorator for sync functions", "status": "passed", "description": "Test with_database_cleanup decorator for synchronous functions.", "start": 1751105549498, "stop": 1751105549499, "uuid": "4b926ced-1cce-416e-be22-6c3d461ead88", "historyId": "f6f059c363048e32c0d008aa4ec22c22", "testCaseId": "f6f059c363048e32c0d008aa4ec22c22", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_with_database_cleanup_decorator_sync", "labels": [{"name": "feature", "value": "Utility Functions"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}