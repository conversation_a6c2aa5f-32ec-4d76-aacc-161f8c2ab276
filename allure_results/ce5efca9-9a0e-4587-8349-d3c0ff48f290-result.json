{"name": "Test async function success after retries", "status": "passed", "description": "Tests that the async function successfully completes after retrying due to deadlocks.", "attachments": [{"name": "Async Retry Attempts", "source": "83e32398-c754-41e8-8043-3592a3bacef3-attachment.txt", "type": "text/plain"}], "start": 1751105550666, "stop": 1751105551089, "uuid": "d8c29b65-51ab-45b6-acc1-2156a35f2c5c", "historyId": "5fa3a9df77cf7f9c814ef156472e7c41", "testCaseId": "5fa3a9df77cf7f9c814ef156472e7c41", "fullName": "tests.smart_retry.test_smart_retry#test_async_success", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}