{"name": "Test schema_translate_map update to different schema", "status": "passed", "description": "Test that `update_schema` correctly updates the schema_translate_map on the engines.", "start": 1751105409598, "stop": 1751105409599, "uuid": "c3494cf9-37c3-4619-a05a-a268e233a9e1", "historyId": "9dfb7e9e89e9d51108508e4a3cb61b87", "testCaseId": "9dfb7e9e89e9d51108508e4a3cb61b87", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_update_schema_updates_schema_translate_map", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}