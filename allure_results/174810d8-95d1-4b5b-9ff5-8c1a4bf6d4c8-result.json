{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Extract field IDs for datatype 'nonexistent'", "status": "passed", "start": 1751105425133, "stop": 1751105425133}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751105425133, "stop": 1751105425133}], "parameters": [{"name": "datatype", "value": "'nonexistent'"}, {"name": "expected_ids", "value": "[]"}], "start": 1751105425133, "stop": 1751105425134, "uuid": "5861ee77-8f89-4176-84bb-46b395e740b4", "historyId": "9f67fdc9024885daef874832b9a67fcf", "testCaseId": "a22222b2720a766a127ad4fd3b149427", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}