{"name": "Test get_jira_users with complete mocking", "status": "passed", "description": "Alternative approach - mock the entire function chain", "attachments": [{"name": "stdout", "source": "bcdeb2b5-c56c-43ac-88e8-7a8e10835f60-attachment.txt", "type": "text/plain"}], "start": 1751105432486, "stop": 1751105434130, "uuid": "f5eef216-ab7b-43c5-9784-aa0849a24f57", "historyId": "2b1fbbb81eea6ae8cb76b5d79593a3c3", "testCaseId": "2b1fbbb81eea6ae8cb76b5d79593a3c3", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_complete_mock", "labels": [{"name": "story", "value": "Get Jira Users"}, {"name": "feature", "value": "Jira API"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}