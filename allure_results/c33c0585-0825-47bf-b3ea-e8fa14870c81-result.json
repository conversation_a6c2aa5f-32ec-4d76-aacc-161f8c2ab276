{"name": "Test EntryDetails creation", "status": "passed", "description": "Test creating EntryDetails with all fields.", "start": 1751105548268, "stop": 1751105548269, "uuid": "f1e3af80-9c7e-45f5-b1db-9bccf994e77a", "historyId": "488fc987e6c1e22c41d4a0d87be8b625", "testCaseId": "488fc987e6c1e22c41d4a0d87be8b625", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_creation", "labels": [{"name": "story", "value": "Entry Details Management"}, {"name": "feature", "value": "EntryDetails"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}