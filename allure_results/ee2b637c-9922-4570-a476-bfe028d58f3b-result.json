{"name": "Verify all field names are extracted correctly", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751105425478, "stop": 1751105425478}, {"name": "Extract field names", "status": "passed", "start": 1751105425478, "stop": 1751105425478}, {"name": "Verify the field names match the expected values", "status": "passed", "start": 1751105425478, "stop": 1751105425478}], "start": 1751105425476, "stop": 1751105425478, "uuid": "2c019c7b-47a1-4236-8f16-c5f0cc71a95f", "historyId": "0abc7dc6613ab75d935bacfff0ef3977", "testCaseId": "0abc7dc6613ab75d935bacfff0ef3977", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_names", "labels": [{"name": "story", "value": "Extract field names"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}