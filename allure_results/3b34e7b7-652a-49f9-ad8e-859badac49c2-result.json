{"name": "Test build_entry_details function", "status": "passed", "description": "Test build_entry_details function.", "start": 1751105429763, "stop": 1751105429764, "uuid": "8ccb466d-7648-4682-a8fd-01c54520b3a3", "historyId": "2058bf5ccc8cc3cacc06ee9932010ee2", "testCaseId": "2058bf5ccc8cc3cacc06ee9932010ee2", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_build_entry_details", "labels": [{"name": "story", "value": "Helper Functions and Decorators"}, {"name": "feature", "value": "Utility Functions"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}