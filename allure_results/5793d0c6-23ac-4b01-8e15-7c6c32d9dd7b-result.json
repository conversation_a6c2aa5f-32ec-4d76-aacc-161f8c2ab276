{"name": "Test get_jira_users with mocked services", "status": "passed", "description": "Test get_jira_users with properly mocked dependencies using dependency injection", "attachments": [{"name": "stdout", "source": "1a80afda-1650-454f-8b6b-8a83141c88e5-attachment.txt", "type": "text/plain"}], "start": 1751105432130, "stop": 1751105432475, "uuid": "4b4e1451-47e9-42b7-bd61-e7ab8606e9cd", "historyId": "0fc556b318c393981f356e565c5e82b3", "testCaseId": "0fc556b318c393981f356e565c5e82b3", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users", "labels": [{"name": "story", "value": "Get Jira Users"}, {"name": "feature", "value": "Jira API"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}