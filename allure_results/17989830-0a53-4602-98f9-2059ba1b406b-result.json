{"name": "Test database_ro session manager resolution", "status": "passed", "description": "Test that `database_ro` resolves correctly.", "start": 1751105531117, "stop": 1751105531119, "uuid": "5010613f-829e-45c1-9f0a-91085cab10da", "historyId": "8b6644d98436d2dab1e72c23c5f6d1d6", "testCaseId": "8b6644d98436d2dab1e72c23c5f6d1d6", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_database_ro", "labels": [{"name": "severity", "value": "minor"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}