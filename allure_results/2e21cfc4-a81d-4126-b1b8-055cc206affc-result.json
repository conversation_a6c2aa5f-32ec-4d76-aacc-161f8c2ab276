{"name": "Test SQLAlchemy engine creation with mocked psycopg2", "status": "passed", "description": "Test SQLAlchemy engine creation with psycopg2 (mocked).", "attachments": [{"name": "stdout", "source": "3649bc46-ba40-401d-b0fe-424c1f4fe6f3-attachment.txt", "type": "text/plain"}], "start": 1751105429932, "stop": 1751105429934, "uuid": "1dcd717a-0d6d-4fab-a624-b31fe07e4d98", "historyId": "33005b3b45e1e472468419dae881ce48", "testCaseId": "33005b3b45e1e472468419dae881ce48", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_engine_creation_psycopg2", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}