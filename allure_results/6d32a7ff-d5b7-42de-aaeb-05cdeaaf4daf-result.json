{"name": "Test pg_ro_entry resolution", "status": "passed", "description": "Test that `pg_ro_entry` resolves correctly.", "start": 1751105411480, "stop": 1751105413910, "uuid": "a398c713-f1c5-4954-8a9d-a13f42f37797", "historyId": "2034b186413dbb8980186902dfa71797", "testCaseId": "2034b186413dbb8980186902dfa71797", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_ro_entry", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test against real db"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}