{"name": "Test get_jira_users with simplified DI approach", "status": "passed", "description": "Simplified test using proper dependency injection pattern", "attachments": [{"name": "stdout", "source": "7256cf83-13c2-416c-a13a-3a729e386a1d-attachment.txt", "type": "text/plain"}], "start": 1751105434140, "stop": 1751105434446, "uuid": "9c08e232-43cf-4df9-a838-92591e7bc347", "historyId": "abd1892a59abff8c04642f5eaf2d2e70", "testCaseId": "abd1892a59abff8c04642f5eaf2d2e70", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_simplified", "labels": [{"name": "story", "value": "Get Jira Users"}, {"name": "feature", "value": "Jira API"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}