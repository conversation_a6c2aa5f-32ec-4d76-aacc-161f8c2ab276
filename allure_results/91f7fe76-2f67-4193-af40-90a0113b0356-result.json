{"name": "test_response_format_consistency", "status": "passed", "description": "Test that all response formats are consistent", "attachments": [{"name": "stdout", "source": "ed66ca5b-7cef-4920-acdc-8be45e860477-attachment.txt", "type": "text/plain"}], "start": 1751105551889, "stop": 1751105551891, "uuid": "859b6991-66d7-4a54-9c27-4ff994a5c2da", "historyId": "ec011eabf47961168b72967409d1dca6", "testCaseId": "ec011eabf47961168b72967409d1dca6", "fullName": "tests.test_fetch_with_retries_consistency#test_response_format_consistency", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}