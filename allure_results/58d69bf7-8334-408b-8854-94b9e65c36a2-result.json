{"name": "Test sync function max retries limit", "status": "passed", "description": "Tests that the sync function raises TimeoutError after reaching the max retries limit.", "start": 1751105430393, "stop": 1751105430995, "uuid": "4a76c2d2-d400-4efe-88a7-3d408c44d5b4", "historyId": "f1e95fa5975375c3952d8ab71383d874", "testCaseId": "f1e95fa5975375c3952d8ab71383d874", "fullName": "tests.smart_retry.test_smart_retry#test_sync_max_retries", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}