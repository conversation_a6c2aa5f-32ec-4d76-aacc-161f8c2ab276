{"name": "Test schema_translate_map update to different schema", "status": "passed", "description": "Test that `update_schema` correctly updates the schema_translate_map on the engines.", "start": 1751105409462, "stop": 1751105409464, "uuid": "b154758a-ade9-4e49-87f8-949e39b05808", "historyId": "8bb195735329535ad36b570300f1657e", "testCaseId": "8bb195735329535ad36b570300f1657e", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_update_schema_updates_schema_translate_map", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}