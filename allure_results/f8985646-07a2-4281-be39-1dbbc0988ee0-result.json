{"name": "test_fetch_with_retries_success", "status": "passed", "description": "Test that successful responses have consistent format", "attachments": [{"name": "stdout", "source": "cd2d3804-03b9-4411-90ee-73c996d5bffe-attachment.txt", "type": "text/plain"}], "start": 1751105432091, "stop": 1751105432094, "uuid": "8b5eb30c-41df-4a5a-ba2c-1052620bb1b2", "historyId": "c37f1a4437e742ab070ba08b2ee81145", "testCaseId": "c37f1a4437e742ab070ba08b2ee81145", "fullName": "tests.test_fetch_with_retries_consistency#test_fetch_with_retries_success", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}