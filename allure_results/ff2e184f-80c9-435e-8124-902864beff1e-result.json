{"name": "Test build_entry_details function", "status": "passed", "description": "Test build_entry_details function.", "start": 1751105549451, "stop": 1751105549454, "uuid": "c95c83a3-dae4-4385-86d9-c7ff6423a569", "historyId": "2058bf5ccc8cc3cacc06ee9932010ee2", "testCaseId": "2058bf5ccc8cc3cacc06ee9932010ee2", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_build_entry_details", "labels": [{"name": "feature", "value": "Utility Functions"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}