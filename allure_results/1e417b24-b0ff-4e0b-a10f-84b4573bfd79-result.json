{"name": "Test schema update functionality", "status": "passed", "description": "Test schema update functionality.", "start": 1751105548315, "stop": 1751105548323, "uuid": "2d099df4-d170-4a56-8657-0cfc66fff171", "historyId": "8b22328d963c4804535f398ee225f666", "testCaseId": "8b22328d963c4804535f398ee225f666", "fullName": "tests.container.test_containers_comprehensive.TestPostgresSessionManager#test_update_schema", "labels": [{"name": "feature", "value": "PostgresSessionManager"}, {"name": "story", "value": "Database Session Management"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}