{"name": "Test singleton behavior of <PERSON><PERSON><PERSON><PERSON> manager", "status": "passed", "description": "Test that `keepass_manager` is resolved as a singleton.", "start": 1751105531122, "stop": 1751105531122, "uuid": "d99b4730-84d1-48a2-b92f-ddb0e3436011", "historyId": "a476b6ffafe8b5526f25f882b40d03db", "testCaseId": "a476b6ffafe8b5526f25f882b40d03db", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_keepass_manager_singleton", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}