{"name": "test_successful_fetch_returns_result", "status": "passed", "description": "C:\\Users\\<USER>\\PycharmProjects\\airflow\\tests\\features\\fetch_with_retries.feature: Successful fetch returns result", "attachments": [{"name": "stdout", "source": "96a84528-b5c8-4502-9260-065924d5bc13-attachment.txt", "type": "text/plain"}], "start": 1751105551747, "stop": 1751105551759, "uuid": "4a107f2b-3f9e-4507-8440-55bc284f7753", "historyId": "30881ec731ea11d5f28f7606404380db", "testCaseId": "30881ec731ea11d5f28f7606404380db", "fullName": "tests.steps.test_fetch_steps#test_successful_fetch_returns_result", "labels": [{"name": "parentSuite", "value": "tests.steps"}, {"name": "suite", "value": "test_fetch_steps"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.steps.test_fetch_steps"}]}