{"name": "Test container initialization", "status": "passed", "description": "Test DatabaseSessionManagerContainer initialization.", "start": 1751105429260, "stop": 1751105429345, "uuid": "55e7f753-772a-49f8-9982-a5afdf65827c", "historyId": "47fb875795c838bf2b5243f61d43b93d", "testCaseId": "47fb875795c838bf2b5243f61d43b93d", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_container_initialization", "labels": [{"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}