{"name": "Test schema_translate_map update to different schema", "status": "passed", "description": "Test that `update_schema` correctly updates the schema_translate_map on the engines.", "start": 1751105531137, "stop": 1751105531137, "uuid": "dc8786b9-96a1-42e1-828d-208f3f2c0e45", "historyId": "9dfb7e9e89e9d51108508e4a3cb61b87", "testCaseId": "9dfb7e9e89e9d51108508e4a3cb61b87", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_update_schema_updates_schema_translate_map", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}