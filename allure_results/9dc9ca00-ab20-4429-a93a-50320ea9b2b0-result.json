{"name": "Test pg_rw_entry resolution", "status": "passed", "description": "Test that `pg_rw_entry` resolves correctly.", "start": 1751105409559, "stop": 1751105409559, "uuid": "ca6252f0-8a59-4d52-a0a6-193dcbf8c6a8", "historyId": "77b902432dae5417ef6ad6b52bfc1441", "testCaseId": "77b902432dae5417ef6ad6b52bfc1441", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_pg_rw_entry", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}