{"name": "Test process_jira_issues with mocked services", "status": "broken", "statusDetails": {"message": "AttributeError: 'Provide' object has no attribute 'config'", "trace": "self = <airflow.tests.test_jira_api_mocks.TestJiraIssues object at 0x00000193F9341C40>, mock_jira_entry = <MagicMock spec='EntryDetails' id='1735073930592'>\nmock_app_container = <MagicMock spec='ApplicationContainer' id='1735073991232'>, mock_db_manager = <MagicMock spec='PostgresSessionManager' id='1735074176496'>\n\n    @pytest.mark.asyncio\n    @allure.title(\"Test process_jira_issues with mocked services\")\n    async def test_process_jira_issues(self, mock_jira_entry, mock_app_container, mock_db_manager):\n        \"\"\"Test process_jira_issues function with mocked external services\"\"\"\n    \n        # Mock project key and scope\n        project_key = \"TEST\"\n        scope = \"project\"\n        initial_load = True\n    \n        # Setup DI containers\n        app_container = ApplicationContainer()\n        logger_container = LoggerContainer()\n        jira_container = JiraEntryDetailsContainer()\n    \n        # Create mocks\n        mock_logger = MagicMock()\n        mock_entry = MagicMock()\n        mock_entry.url = \"https://example.atlassian.net/\"\n        mock_entry.custom_properties = {\"Authorization\": \"Bearer testtoken\"}\n    \n    \n        # Setup overrides\n        logger_container.logger.override(mock_logger)\n        jira_container.jira_entry_details.override(mock_entry)\n        app_container.logger_container.override(logger_container)\n        app_container.database_rw.override(mock_db_manager)\n        app_container.schema.override(\"public\")\n    \n        # Wire containers\n        logger_container.wire(modules=[\"dags.data_pipeline.utility_code\"])\n        jira_container.wire(modules=[\"dags.data_pipeline.utility_code\"])\n        app_container.wire(modules=[\"dags.data_pipeline.utility_code\"])\n    \n        # Patch the necessary functions and classes\n        with (\n            patch(\"dags.data_pipeline.utility_code.get_issues_from_jira_jql\") as mock_get_issues,\n            patch(\"aiohttp.ClientSession\") as mock_client_session,\n            patch(\"dags.data_pipeline.utility_code.fetch_with_retries_get\") as mock_fetch\n        ):\n    \n            # Configure the mock session\n            mock_session = AsyncMock()\n            mock_client_session_instance = AsyncMock()\n            mock_client_session_instance.__aenter__.return_value = mock_session\n            mock_client_session.return_value = mock_client_session_instance\n    \n            # Return timezone info from fetch\n            mock_fetch.return_value = {'result': {'timeZone': 'Asia/Kolkata'}}\n            mock_db_manager.get_last_run_timestamp = AsyncMock(\n                return_value=datetime(2024, 1, 1, 12, 0, tzinfo=timezone.utc)\n            )\n    \n            mock_get_issues.return_value = None\n    \n            # Create mock for enhanced DB manager\n            mock_db_enhanced = MagicMock()\n    \n            # Create mock for the result of update_schema()\n            mock_schema_manager = MagicMock()\n    \n            # Create mock async session context manager\n            mock_async_session_context = AsyncMock()\n            mock_async_session = AsyncMock()\n            mock_async_session_context.__aenter__.return_value = mock_async_session\n            mock_async_session_context.__aexit__.return_value = None\n    \n            # Wire the chain:\n            # database_rw_enhanced().update_schema(project_key).async_session()\n            mock_schema_manager.async_session.return_value = mock_async_session_context\n            mock_db_enhanced.update_schema.return_value = mock_schema_manager\n    \n            # Apply override\n            app_container.database_rw_enhanced.override(mock_db_enhanced)\n    \n            # Simulated return from execute()\n            mock_result = MagicMock()\n            mock_result.scalar_one_or_none.return_value = datetime(2023, 12, 31, 10, 0, tzinfo=timezone.utc)\n    \n            # Assign it to async mock\n            mock_async_session.execute.return_value = mock_result\n    \n            try:\n                # Call the function with our mocked dependencies\n>               result = await uc.process_jira_issues(\n                    project_key=project_key,\n                    scope=scope,\n                    initial_load=initial_load,\n                    jira_entry=mock_entry,\n                    app_container=app_container,\n    \n                )\n\ntests\\test_jira_api_mocks.py:556: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1066: in _patched\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\ndags\\data_pipeline\\utility_code.py:4433: in process_jira_issues\n    handle_exception(e)\n.venv\\Lib\\site-packages\\dependency_injector\\wiring.py:1089: in _patched\n    return fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^\ndags\\data_pipeline\\utility_code.py:3736: in handle_exception\n    raise e\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nproject_key = 'TEST', scope = 'project', initial_load = True, app_container = <dependency_injector.containers.DynamicContainer object at 0x00000193FA7B50A0>, jira_entry = <MagicMock id='1735074303776'>\nq_container = <dependency_injector.wiring.Provide object at 0x00000193F92EEE10>, my_logger = <MagicMock id='1735074312704'>\n\n    @inject\n    async def process_jira_issues(\n            project_key: str,\n            scope: str,\n            initial_load: bool,\n            app_container: DeclarativeContainer = Provide[ApplicationContainer],\n            jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],\n            q_container: DynamicContainer = Provide[QueueContainer],\n            my_logger: Logger = Provide[LoggerContainer.logger],\n    ):\n        \"\"\"\n        Process JIRA issues with improved error handling, transaction management,\n        and better separation of concerns.\n        \"\"\"\n        try:\n            my_logger.info(f\"Processing project: {project_key}, scope: {scope}\")\n            # Initialize timezone and process marker\n            time_zone, process_start_marker = await _initialize_jira_session(\n                jira_entry, my_logger\n            )\n    \n            # Setup database schema and get last run datetime\n            app_container.schema.override(project_key)\n>           q_container.config.override({\"schema_name\": project_key})\n            ^^^^^^^^^^^^^^^^^^\nE           AttributeError: 'Provide' object has no attribute 'config'\n\ndags\\data_pipeline\\utility_code.py:4390: AttributeError"}, "description": "Test process_jira_issues function with mocked external services", "attachments": [{"name": "stdout", "source": "3469763e-f4b6-4ff5-b9d8-48f36<PERSON><PERSON>e9-attachment.txt", "type": "text/plain"}], "start": 1751105434754, "stop": 1751105435086, "uuid": "3dd273e1-70f5-43df-abc9-bfb9f28af6f7", "historyId": "57c0a3912d550bd0e41d39a4e4dee4f2", "testCaseId": "57c0a3912d550bd0e41d39a4e4dee4f2", "fullName": "tests.test_jira_api_mocks.TestJiraIssues#test_process_jira_issues", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Process Jira Issues"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraIssues"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}