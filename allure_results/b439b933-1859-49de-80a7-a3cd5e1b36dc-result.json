{"name": "Test psycopg3 is not installed", "status": "passed", "description": "Test that psycopg3 (psycopg) is not installed to avoid conflicts.", "attachments": [{"name": "stdout", "source": "f9696a4d-4c03-4c57-b299-33d10e16e7fb-attachment.txt", "type": "text/plain"}], "start": 1751105549562, "stop": 1751105549565, "uuid": "f1e612b2-f5d8-4000-87ca-dae166e78f67", "historyId": "8016d8d7a23430848fd1fc23ccedc42d", "testCaseId": "8016d8d7a23430848fd1fc23ccedc42d", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_psycopg3_not_installed", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}