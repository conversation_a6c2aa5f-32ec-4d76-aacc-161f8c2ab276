{"name": "Test build_entry_details with missing entry", "status": "passed", "description": "Test build_entry_details when entry is not found.", "start": 1751105549508, "stop": 1751105549509, "uuid": "90bea568-faf6-437e-a09a-916b9bb93043", "historyId": "6f6b1abba38909e11586d1db92274581", "testCaseId": "6f6b1abba38909e11586d1db92274581", "fullName": "tests.container.test_containers_comprehensive.TestErrorHandling#test_build_entry_details_missing_entry", "labels": [{"name": "feature", "value": "Erro<PERSON>"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}