{"name": "Test pg_ro_entry resolution with different schema", "status": "passed", "description": "Test that `pg_ro_entry` resolves with a different schema.", "start": 1751105531130, "stop": 1751105531130, "uuid": "700fe213-d67d-48b5-b53d-c922be89e868", "historyId": "b67bc84fb4fbf8502c49af441d9c8949", "testCaseId": "b67bc84fb4fbf8502c49af441d9c8949", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_pg_ro_entry_different_schema", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}