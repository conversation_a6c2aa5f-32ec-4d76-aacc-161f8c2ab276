{"name": "Test pg_ro_entry resolution", "status": "passed", "description": "Test that `pg_ro_entry` resolves correctly.", "start": 1751105530997, "stop": 1751105530998, "uuid": "5824591e-d01a-4b28-84c6-3b9db957c378", "historyId": "591a87a01cbf5ec956bb0a8dd962a204", "testCaseId": "591a87a01cbf5ec956bb0a8dd962a204", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_pg_ro_entry", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}