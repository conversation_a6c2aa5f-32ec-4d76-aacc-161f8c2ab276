{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751105543809, "stop": 1751105543809}, {"name": "Extract field IDs for datatype 'datetime'", "status": "passed", "start": 1751105543809, "stop": 1751105543809}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751105543809, "stop": 1751105543809}], "parameters": [{"name": "datatype", "value": "'datetime'"}, {"name": "expected_ids", "value": "['created', 'resolutiondate', 'updated', 'statuscategorychangedate']"}], "start": 1751105543809, "stop": 1751105543809, "uuid": "b2d77e18-717e-4534-a1e3-e87f4309b450", "historyId": "32a2b57aabe04c121de3a2b6939bbf5b", "testCaseId": "77a2f6d4d2ec5a0725fb029808f275a1", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}