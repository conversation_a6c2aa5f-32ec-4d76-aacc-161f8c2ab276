{"name": "Test dependency injection basics", "status": "passed", "description": "Test basic dependency injection functionality.", "attachments": [{"name": "stdout", "source": "86bdac45-1c76-43be-a4f7-c0a55144a6b8-attachment.txt", "type": "text/plain"}], "start": 1751105429179, "stop": 1751105429180, "uuid": "af7b0c0b-5d05-4bb5-8399-a7843ca89af9", "historyId": "351fdb5de703a5d815df1db26de2291b", "testCaseId": "351fdb5de703a5d815df1db26de2291b", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_dependency_injection_basics", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}