{"name": "Test database provider resolution", "status": "passed", "description": "Test that database providers resolve correctly.", "start": 1751105429435, "stop": 1751105429507, "uuid": "30c06684-4027-4aee-81fc-643b5f68bf15", "historyId": "979f2ad82379efb258a9a431b7ed212b", "testCaseId": "979f2ad82379efb258a9a431b7ed212b", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_database_provider_resolution", "labels": [{"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}