{"name": "Verify all field names are extracted correctly", "status": "passed", "steps": [{"name": "Extract field names", "status": "passed", "start": 1751105543653, "stop": 1751105543653}, {"name": "Verify the field names match the expected values", "status": "passed", "start": 1751105543653, "stop": 1751105543653}], "start": 1751105543652, "stop": 1751105543653, "uuid": "40a9d0d4-ef1d-476c-8310-d111bed16678", "historyId": "3f66928e85f8dc48f62dd97d6e24384e", "testCaseId": "3f66928e85f8dc48f62dd97d6e24384e", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_names", "labels": [{"name": "story", "value": "Extract field names"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}