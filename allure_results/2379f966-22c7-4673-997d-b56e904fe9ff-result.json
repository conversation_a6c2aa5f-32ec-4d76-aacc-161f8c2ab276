{"name": "Test schema_rw entry details", "status": "passed", "description": "Test that the schema_rw resolves correctly with the appropriate title.", "start": 1751105429021, "stop": 1751105429024, "uuid": "2eb62659-3ed0-45d7-a961-2ce79f9921ab", "historyId": "6a1e3c252f59c65c9754e994ae43f9ae", "testCaseId": "6a1e3c252f59c65c9754e994ae43f9ae", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_schema_rw_entry_details", "labels": [{"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}