{"name": "Test queue selection for \"acq\" schema", "status": "passed", "description": "Test that the 'acq' schema returns the correct queue instances.", "start": 1751105548096, "stop": 1751105548097, "uuid": "3517e9fd-bdee-4e30-bdec-f6b0bdf2becc", "historyId": "743e44683ef25faa47b63de48f52eafa", "testCaseId": "743e44683ef25faa47b63de48f52eafa", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_queue_selector_acq", "labels": [{"name": "story", "value": "Test queue selection for different schemas"}, {"name": "epic", "value": "Queue Handling"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/acq-schema", "name": "http://testcase-link.com/acq-schema"}]}