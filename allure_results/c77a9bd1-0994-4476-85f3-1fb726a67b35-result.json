{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751105543780, "stop": 1751105543780}, {"name": "Extract field IDs for datatype 'progress'", "status": "passed", "start": 1751105543780, "stop": 1751105543780}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751105543780, "stop": 1751105543780}], "parameters": [{"name": "datatype", "value": "'progress'"}, {"name": "expected_ids", "value": "['aggregateprogress', 'progress']"}], "start": 1751105543780, "stop": 1751105543780, "uuid": "4d91d6b3-a49f-48aa-9e6d-4e7a8b74c195", "historyId": "18d9bde5151d9e086ea2e2fe68514c69", "testCaseId": "77a2f6d4d2ec5a0725fb029808f275a1", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}