received jira_entry of type: <class 'unittest.mock.MagicMock'>
received app_container of type: <class 'dependency_injector.containers.DynamicContainer'>
<MagicMock id='1735073246544'>
['DATABASE_URL', 'DATABASE_URL_ASYNC', '__annotations__', '__class__', '__del__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_async_dispose', '_create_engine', '_create_engine_async', '_instances', 'aclose', 'async_session', 'cleanup_all_instances', 'close', 'closed', 'engine', 'engine_async', 'entry', 'get_schemas', 'get_schemas_async', 'logger', 'rw', 'schema', 'session', 'update_schema']
