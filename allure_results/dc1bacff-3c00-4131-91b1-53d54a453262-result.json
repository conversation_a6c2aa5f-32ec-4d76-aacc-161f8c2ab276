{"name": "Test pg_rw_entry resolution", "status": "passed", "description": "Test that `pg_rw_entry` resolves correctly.", "start": 1751105530991, "stop": 1751105530992, "uuid": "1e4f6632-e245-46d0-b7e1-a2d2957d116a", "historyId": "70158ed251a6a406d2b5125f4d666a99", "testCaseId": "70158ed251a6a406d2b5125f4d666a99", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_pg_rw_entry", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}