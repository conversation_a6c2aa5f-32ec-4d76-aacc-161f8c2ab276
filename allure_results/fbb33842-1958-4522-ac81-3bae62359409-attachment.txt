[31m[1mERROR   [0m asyncio:base_events.py:1833 Task was destroyed but it is pending!
task: <Task pending name='Task-14' coro=<PostgresSessionManager._async_dispose() running at C:\Users\<USER>\PycharmProjects\airflow\dags\data_pipeline\containers.py:431>>
[31m[1mERROR   [0m dags.data_pipeline.debug_utils:debug_utils.py:152 [HTTP] Request error: POST https://example.atlassian.net//rest/api/3/search/approximate-count after 0.00s: 'coroutine' object does not support the asynchronous context manager protocol