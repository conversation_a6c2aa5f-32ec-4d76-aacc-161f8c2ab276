{"name": "Test singleton behavior of pg_rw_entry", "status": "passed", "description": "Test that `pg_rw_entry` is resolved as a singleton.", "start": 1751105531125, "stop": 1751105531125, "uuid": "f987ea14-3300-4b8a-b9a4-7683960d821e", "historyId": "719098da675a9cb4a8b9ca0130a07c7b", "testCaseId": "719098da675a9cb4a8b9ca0130a07c7b", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_pg_rw_entry_singleton", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}