{"name": "Test singleton behavior of pg_rw_entry", "status": "passed", "description": "Test that `pg_rw_entry` is resolved as a singleton.", "start": 1751105409586, "stop": 1751105409587, "uuid": "e8bd8b6b-aff4-46cb-aef7-0b6ba87ad3ad", "historyId": "719098da675a9cb4a8b9ca0130a07c7b", "testCaseId": "719098da675a9cb4a8b9ca0130a07c7b", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_pg_rw_entry_singleton", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}