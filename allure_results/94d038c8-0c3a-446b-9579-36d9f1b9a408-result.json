{"name": "Verify all field names are extracted correctly", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751105543773, "stop": 1751105543773}, {"name": "Extract field names", "status": "passed", "start": 1751105543773, "stop": 1751105543773}, {"name": "Verify the field names match the expected values", "status": "passed", "start": 1751105543773, "stop": 1751105543773}], "start": 1751105543773, "stop": 1751105543774, "uuid": "b3565f01-c4da-4403-bfa5-6525c9227c31", "historyId": "0abc7dc6613ab75d935bacfff0ef3977", "testCaseId": "0abc7dc6613ab75d935bacfff0ef3977", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_names", "labels": [{"name": "story", "value": "Extract field names"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}