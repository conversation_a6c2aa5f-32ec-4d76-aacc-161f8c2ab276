{"name": "test_container_composition", "status": "passed", "description": "Test container composition pattern.", "attachments": [{"name": "stdout", "source": "9445b768-9953-43a6-b293-71b6aa4c31c5-attachment.txt", "type": "text/plain"}], "start": 1751105564718, "stop": 1751105564721, "uuid": "f136675d-39fd-4216-9ab7-e79b7322d645", "historyId": "2778b8c20bf5d29fc75de5044277354a", "testCaseId": "2778b8c20bf5d29fc75de5044277354a", "fullName": "tests.test_logger_refactor#test_container_composition", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}