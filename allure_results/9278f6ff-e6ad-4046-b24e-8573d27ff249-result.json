{"name": "Test PostgresSessionManager operations after close", "status": "passed", "description": "Test that operations fail after manager is closed.", "start": 1751105549520, "stop": 1751105549529, "uuid": "18f5ba1f-34ab-46ce-a41c-96f5f923c3c5", "historyId": "ceac9f0658d89a4fab0390e7a9267a4a", "testCaseId": "ceac9f0658d89a4fab0390e7a9267a4a", "fullName": "tests.container.test_containers_comprehensive.TestErrorHandling#test_operations_after_close", "labels": [{"name": "feature", "value": "Erro<PERSON>"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}