{"name": "Test Jira entry details resolution for JIRA_ENTRY", "status": "passed", "description": "Test that the `jira_entry_details` resolves correctly with the expected username and URL.", "start": 1751105543879, "stop": 1751105543881, "uuid": "61cc2551-53de-4ce6-bf0f-bd90603240f9", "historyId": "f79231333c85f303d30dacb866300a48", "testCaseId": "f79231333c85f303d30dacb866300a48", "fullName": "tests.container.jira_entry_details.test_jira_entry_details.TestJiraEntryDetailsContainer#test_jira_entry_details", "labels": [{"name": "story", "value": "Test Jira entry details resolution"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "JiraEntryDetailsContainer"}, {"name": "parentSuite", "value": "tests.container.jira_entry_details"}, {"name": "suite", "value": "test_jira_entry_details"}, {"name": "subSuite", "value": "TestJiraEntryDetailsContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.jira_entry_details.test_jira_entry_details"}]}