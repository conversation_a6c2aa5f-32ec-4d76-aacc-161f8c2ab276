{"name": "Test queue instances are singleton across multiple requests", "status": "passed", "description": "Test that the same instance of queues is returned for multiple requests.", "start": 1751105548122, "stop": 1751105548123, "uuid": "0d60592d-b2e8-4b61-ae6a-45875f276475", "historyId": "3c21c94e1c86ae502e8c5810e7bff028", "testCaseId": "3c21c94e1c86ae502e8c5810e7bff028", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_singleton_queue_instances", "labels": [{"name": "story", "value": "Test queue selection for different schemas"}, {"name": "epic", "value": "Queue Handling"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "severity", "value": "minor"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/singleton-queues", "name": "http://testcase-link.com/singleton-queues"}]}