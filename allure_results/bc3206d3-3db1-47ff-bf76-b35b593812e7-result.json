{"name": "Test ApplicationContainer initialization", "status": "passed", "description": "Test ApplicationContainer initialization.", "start": 1751105429512, "stop": 1751105429615, "uuid": "67ce0087-08bf-4f08-8c9e-aa7d77a7674b", "historyId": "aaf428432a2a9ef49b48bab561b58ecf", "testCaseId": "aaf428432a2a9ef49b48bab561b58ecf", "fullName": "tests.container.test_containers_comprehensive.TestApplicationContainer#test_application_container_init", "labels": [{"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}