{"name": "Verify all field names are extracted correctly", "status": "passed", "steps": [{"name": "Extract field names", "status": "passed", "start": 1751105424780, "stop": 1751105424780}, {"name": "Verify the field names match the expected values", "status": "passed", "start": 1751105424780, "stop": 1751105424780}], "start": 1751105424780, "stop": 1751105424784, "uuid": "183dfae8-226f-4c2b-b617-e6ed81fa4858", "historyId": "3f66928e85f8dc48f62dd97d6e24384e", "testCaseId": "3f66928e85f8dc48f62dd97d6e24384e", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_names", "labels": [{"name": "story", "value": "Extract field names"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}