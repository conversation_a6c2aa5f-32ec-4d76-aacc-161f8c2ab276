{"name": "Test database_rw session manager resolution", "status": "passed", "description": "Test that `database_rw` resolves correctly.", "start": 1751105414140, "stop": 1751105416387, "uuid": "d4d84686-0112-4ada-8c0c-ced850796d6c", "historyId": "15b52c4a1d7cfac51f18773d07adcdb3", "testCaseId": "15b52c4a1d7cfac51f18773d07adcdb3", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_database_plat_rw", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "minor"}, {"name": "story", "value": "Test against real db"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}