{"name": "Test invalid retries configuration", "status": "passed", "description": "Tests that the function raises ValueError for invalid retry configurations.", "attachments": [{"name": "Invalid Retries Test", "source": "17408b01-77a0-4d53-a413-ed0b648eb0de-attachment.txt", "type": "text/plain"}], "start": 1751105432038, "stop": 1751105432039, "uuid": "2266fb3d-3a20-4992-b3eb-e2c902df184e", "historyId": "4c941c6537b3e952e6e9cc84b1f8bf4f", "testCaseId": "4c941c6537b3e952e6e9cc84b1f8bf4f", "fullName": "tests.smart_retry.test_smart_retry#test_invalid_retries", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}