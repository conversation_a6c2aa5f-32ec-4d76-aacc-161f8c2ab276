{"name": "test_fetch_with_retries_failure", "status": "passed", "description": "Test that failed responses have consistent format", "attachments": [{"name": "stdout", "source": "c95a8260-7d7c-4689-b3ad-481c564e1c6c-attachment.txt", "type": "text/plain"}], "start": 1751105551855, "stop": 1751105551865, "uuid": "f931e7b2-c3bb-46ef-8a0d-d60ec34903ec", "historyId": "df34c252c1894c3c42fa82c6dc510698", "testCaseId": "df34c252c1894c3c42fa82c6dc510698", "fullName": "tests.test_fetch_with_retries_consistency#test_fetch_with_retries_failure", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}