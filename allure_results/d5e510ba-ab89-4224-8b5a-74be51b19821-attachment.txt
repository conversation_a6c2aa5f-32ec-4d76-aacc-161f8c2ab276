Would verify that <MagicMock name='mock.fetchone().project' id='1332017595360'> == Managed Services for column project
Would convert timezone for updated: <MagicMock name='mock.fetchone().updated' id='1332017520112'> to Asia/Kolkata
Would verify that <MagicMock name='mock.fetchone().updated' id='1332017520112'> == 2022-03-21 00:00:00+05:30 for column updated
Would verify that <MagicMock name='mock.fetchone().project' id='1332000387776'> == Managed Services for column project
Would verify that <MagicMock name='mock.fetchone().updated' id='1332000375008'> == mocked_scalar_value for column updated
Would verify that <MagicMock name='mock.fetchone().project' id='1332000312160'> == Managed Services for column project
Would verify that <MagicMock name='mock.fetchone().updated' id='1332000310384'> == mocked_scalar_value for column updated
