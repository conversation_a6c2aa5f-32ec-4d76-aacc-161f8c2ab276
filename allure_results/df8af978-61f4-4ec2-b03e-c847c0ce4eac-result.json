{"name": "initiative_attribute: test upsert with conflict condition", "status": "passed", "description": "\n        Test with_conflict_condition for upsert functionality for InitiativeAttribute table.\n        ", "attachments": [{"name": "stdout", "source": "8b637ed9-6e5c-4ba1-a640-151146a7ae23-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10346, 'TRAIN-1', '2020-03-23', '2022-03-21', 'Managed Services'], [10346, 'TRAIN-1', '2020-03-23', '2019-03-21', 'Managed Services'], [10346, 'TRAIN-1', '2020-03-23', '2020-03-23', 'Managed Services']]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BinaryExpression object at 0x00000193F93E6E40>"}, {"name": "expected_updated", "value": "[Timestamp('2022-03-21 00:00:00+0530', tz='Asia/Kolkata'), None, None]"}, {"name": "should_update", "value": "[True, False, False]"}], "start": 1751105436062, "stop": 1751105436072, "uuid": "772deaf4-1805-4643-84e8-b2fac95608eb", "historyId": "65bc67c4677f35881dafddcd9a587cdd", "testCaseId": "eca30d1044a42541a27f98f1986608cc", "fullName": "tests.upsert_conflict_condition.test_upsert_initiative_attribute.TestUpsertInitiativeAttribute#test_upsert_with_conflict_condition_initiative_attribute", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Upsert Conflict Condition"}, {"name": "requirement", "value": "AIR-51"}, {"name": "story", "value": "Test upsert with conflict condition for initiative_attribute table"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_initiative_attribute"}, {"name": "subSuite", "value": "TestUpsertInitiativeAttribute"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_initiative_attribute"}]}