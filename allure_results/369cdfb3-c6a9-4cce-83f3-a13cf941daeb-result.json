{"name": "Test queue selection for \"acq\" schema", "status": "passed", "description": "Test that the 'acq' schema returns the correct queue instances.", "start": 1751105429110, "stop": 1751105429111, "uuid": "431c8c77-6049-4cf6-ad25-6ad1983fb259", "historyId": "743e44683ef25faa47b63de48f52eafa", "testCaseId": "743e44683ef25faa47b63de48f52eafa", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_queue_selector_acq", "labels": [{"name": "severity", "value": "normal"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "epic", "value": "Queue Handling"}, {"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "story", "value": "Test queue selection for different schemas"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/acq-schema", "name": "http://testcase-link.com/acq-schema"}]}