{"name": "Test basic imports", "status": "passed", "description": "Test that basic required modules can be imported.", "attachments": [{"name": "stdout", "source": "4f257606-d9d0-44ff-b446-f5091711d090-attachment.txt", "type": "text/plain"}], "start": 1751105429148, "stop": 1751105429149, "uuid": "0a2cbd63-86b2-48a0-b41f-f540a5dbc4e9", "historyId": "5bccab9908afef60fcc1a90e8d1aa589", "testCaseId": "5bccab9908afef60fcc1a90e8d1aa589", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_basic_imports", "labels": [{"name": "severity", "value": "blocker"}, {"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}