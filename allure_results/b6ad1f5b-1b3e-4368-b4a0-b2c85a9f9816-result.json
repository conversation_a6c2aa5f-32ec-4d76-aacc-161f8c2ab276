{"name": "Test get_jira_users with complete mocking", "status": "passed", "description": "Alternative approach - mock the entire function chain", "attachments": [{"name": "stdout", "source": "c782b9e9-16ce-4af8-b08e-79bd2230c18c-attachment.txt", "type": "text/plain"}], "start": 1751105552850, "stop": 1751105559785, "uuid": "af438574-a6a2-4ee8-a05c-1e8164d40b4e", "historyId": "2b1fbbb81eea6ae8cb76b5d79593a3c3", "testCaseId": "2b1fbbb81eea6ae8cb76b5d79593a3c3", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_complete_mock", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}