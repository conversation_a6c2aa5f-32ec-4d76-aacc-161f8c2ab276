{"name": "Upsert Test for Issue Classification", "status": "passed", "description": "Test the upsert functionality considering null and non-null values in conflict conditions.", "steps": [{"name": "Iterate over test data and perform upserts", "status": "passed", "start": 1751105564828, "stop": 1751105564840}], "attachments": [{"name": "stdout", "source": "935f6cd4-eec6-41fb-9ef2-a886766d33a1-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10352, 'TRAIN-6', 10346, 10347, 10349, 10352, 'TRAIN-16']]"}, {"name": "expected_updated", "value": "[None]"}, {"name": "should_update", "value": "[False]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BooleanClauseList object at 0x00000136214A7860>"}], "start": 1751105564827, "stop": 1751105564841, "uuid": "c95212d5-96dd-4abf-9015-d6f6cc866ae7", "historyId": "bfed374d5abf96fe05bcf78b03d72c12", "testCaseId": "079f09dafc6eae06e1ec1c4ee0c1442a", "fullName": "tests.upsert_conflict_condition.test_upsert_issue_classification#test_upsert_with_conflict_condition_issue_classification", "labels": [{"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_issue_classification"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_issue_classification"}]}