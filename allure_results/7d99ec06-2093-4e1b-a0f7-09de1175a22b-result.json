{"name": "Test mocked engine creation", "status": "passed", "description": "Test engine creation with mocked SQLAlchemy.", "attachments": [{"name": "stdout", "source": "a9a9899a-6ec9-4b74-a739-ce35bde4a0bd-attachment.txt", "type": "text/plain"}], "start": 1751105429164, "stop": 1751105429167, "uuid": "704ba6a4-4e35-4df2-91cf-68edd51c424f", "historyId": "ab8f7500848a585d8b922a2ed42d4557", "testCaseId": "ab8f7500848a585d8b922a2ed42d4557", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_mocked_engine_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}