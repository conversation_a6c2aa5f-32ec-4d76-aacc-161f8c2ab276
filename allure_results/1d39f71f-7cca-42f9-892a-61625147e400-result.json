{"name": "Test ApplicationContainer initialization", "status": "passed", "description": "Test ApplicationContainer initialization.", "start": 1751105549041, "stop": 1751105549212, "uuid": "bf03fa06-b05a-44e8-b032-e9e504b2e3c0", "historyId": "aaf428432a2a9ef49b48bab561b58ecf", "testCaseId": "aaf428432a2a9ef49b48bab561b58ecf", "fullName": "tests.container.test_containers_comprehensive.TestApplicationContainer#test_application_container_init", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}