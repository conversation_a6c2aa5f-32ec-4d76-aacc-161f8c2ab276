{"name": "Test JiraEntryDetailsContainer Singleton behavior", "status": "passed", "description": "Test that the `jira_entry_details` is resolved as a singleton.", "start": 1751105543884, "stop": 1751105543886, "uuid": "3369c18f-486c-42db-b2e4-efdb0504ce10", "historyId": "b294e07c534a2d66401f773b4f4d4c5f", "testCaseId": "b294e07c534a2d66401f773b4f4d4c5f", "fullName": "tests.container.jira_entry_details.test_jira_entry_details.TestJiraEntryDetailsContainer#test_jira_entry_details_singleton", "labels": [{"name": "story", "value": "Test Jira entry details resolution"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "JiraEntryDetailsContainer"}, {"name": "parentSuite", "value": "tests.container.jira_entry_details"}, {"name": "suite", "value": "test_jira_entry_details"}, {"name": "subSuite", "value": "TestJiraEntryDetailsContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.jira_entry_details.test_jira_entry_details"}]}