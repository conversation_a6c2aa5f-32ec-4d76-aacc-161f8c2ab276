{"name": "Test KeePassManager instantiation", "status": "passed", "description": "Test the instantiation of the KeePass manager with the correct parameters.", "start": 1751105425889, "stop": 1751105428989, "uuid": "50d28485-49dd-4036-99cf-d1e840b3557f", "historyId": "03e0338a03b26223e82cbdc972372012", "testCaseId": "03e0338a03b26223e82cbdc972372012", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_keepass_manager", "labels": [{"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}