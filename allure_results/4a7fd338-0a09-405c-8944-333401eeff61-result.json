{"name": "Test psycopg3 is not installed", "status": "passed", "description": "Test that psycopg3 (psycopg) is not installed to avoid conflicts.", "attachments": [{"name": "stdout", "source": "250b823c-2ebf-4136-9998-371443fd14d8-attachment.txt", "type": "text/plain"}], "start": 1751105429903, "stop": 1751105429905, "uuid": "5926cd62-3ae1-4b59-a9dc-ca3582d9202b", "historyId": "8016d8d7a23430848fd1fc23ccedc42d", "testCaseId": "8016d8d7a23430848fd1fc23ccedc42d", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_psycopg3_not_installed", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}