{"name": "Upsert Test for Issue Classification", "status": "passed", "description": "Test the upsert functionality considering null and non-null values in conflict conditions.", "steps": [{"name": "Iterate over test data and perform upserts", "status": "passed", "start": 1751105564862, "stop": 1751105564876}], "attachments": [{"name": "stdout", "source": "daec14e8-21dc-4b49-be7f-8ffd9718ef70-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10352, 'TRAIN-6', None, 10347, 10349, 10352, 'TRAIN-16']]"}, {"name": "expected_updated", "value": "['TRAIN-16']"}, {"name": "should_update", "value": "[True]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BooleanClauseList object at 0x00000136213AED50>"}], "start": 1751105564861, "stop": 1751105564877, "uuid": "5924bbd3-62a2-4f90-9ff2-c116fca15826", "historyId": "607d7a40a052b74f9230182f8c246a8c", "testCaseId": "079f09dafc6eae06e1ec1c4ee0c1442a", "fullName": "tests.upsert_conflict_condition.test_upsert_issue_classification#test_upsert_with_conflict_condition_issue_classification", "labels": [{"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_issue_classification"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_issue_classification"}]}