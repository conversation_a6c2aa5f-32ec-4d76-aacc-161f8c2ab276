{"name": "Test queue instances are singleton across multiple requests", "status": "passed", "description": "Test that the same instance of queues is returned for multiple requests.", "start": 1751105429125, "stop": 1751105429126, "uuid": "3c4115d8-ba35-4cfc-9258-2aa961d8a825", "historyId": "3c21c94e1c86ae502e8c5810e7bff028", "testCaseId": "3c21c94e1c86ae502e8c5810e7bff028", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_singleton_queue_instances", "labels": [{"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "severity", "value": "minor"}, {"name": "epic", "value": "Queue Handling"}, {"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "story", "value": "Test queue selection for different schemas"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/singleton-queues", "name": "http://testcase-link.com/singleton-queues"}]}