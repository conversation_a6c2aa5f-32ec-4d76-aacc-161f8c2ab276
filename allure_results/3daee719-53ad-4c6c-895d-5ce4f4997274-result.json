{"name": "Upsert Test for Issue Classification", "status": "passed", "description": "Test the upsert functionality considering null and non-null values in conflict conditions.", "steps": [{"name": "Iterate over test data and perform upserts", "status": "passed", "start": 1751105436088, "stop": 1751105436091}], "attachments": [{"name": "stdout", "source": "0b547482-1064-464b-9d29-f01d3a44b5df-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10352, 'TRAIN-6', 10346, 10347, 10349, 10352, 'TRAIN-16']]"}, {"name": "expected_updated", "value": "[None]"}, {"name": "should_update", "value": "[False]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BooleanClauseList object at 0x00000193F93438F0>"}], "start": 1751105436087, "stop": 1751105436091, "uuid": "60e8de1d-0b95-436f-bb70-a5ef389794a0", "historyId": "dd6be75177a95c07e460111652188368", "testCaseId": "079f09dafc6eae06e1ec1c4ee0c1442a", "fullName": "tests.upsert_conflict_condition.test_upsert_issue_classification#test_upsert_with_conflict_condition_issue_classification", "labels": [{"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_issue_classification"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_issue_classification"}]}