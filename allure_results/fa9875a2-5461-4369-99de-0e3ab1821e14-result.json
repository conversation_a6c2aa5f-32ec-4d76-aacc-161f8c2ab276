{"name": "Test lifecycle manager cleanup", "status": "passed", "description": "Test lifecycle manager cleanup functionality.", "start": 1751105548354, "stop": 1751105548367, "uuid": "db9b9ec3-a8c6-4e2c-8f78-7feb38bfe30d", "historyId": "1e12c6cbda6118d11ab839200435dcad", "testCaseId": "1e12c6cbda6118d11ab839200435dcad", "fullName": "tests.container.test_containers_comprehensive.TestManagedPostgresSessionManager#test_lifecycle_manager_cleanup", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Managed Database Sessions"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}