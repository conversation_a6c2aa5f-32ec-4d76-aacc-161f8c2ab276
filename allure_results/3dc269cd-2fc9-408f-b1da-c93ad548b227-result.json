{"name": "test_safe_response_access_patterns", "status": "passed", "description": "Test that our fixed code patterns safely access response data", "attachments": [{"name": "stdout", "source": "9f3cd2ae-4283-4e24-9088-6a3797ed0d4e-attachment.txt", "type": "text/plain"}], "start": 1751105551878, "stop": 1751105551879, "uuid": "d8a2a889-195e-4770-b301-475f1a539547", "historyId": "11089639a12418c3ddda10daa9f6c1eb", "testCaseId": "11089639a12418c3ddda10daa9f6c1eb", "fullName": "tests.test_fetch_with_retries_consistency#test_safe_response_access_patterns", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}