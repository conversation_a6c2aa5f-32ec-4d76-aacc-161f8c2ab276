{"name": "Test dependency injection basics", "status": "passed", "description": "Test basic dependency injection functionality.", "attachments": [{"name": "stdout", "source": "8bd3d021-a515-484e-8643-70713cd97936-attachment.txt", "type": "text/plain"}], "start": 1751105548222, "stop": 1751105548224, "uuid": "4e8144a9-125f-4fa5-a6ba-8af968002085", "historyId": "351fdb5de703a5d815df1db26de2291b", "testCaseId": "351fdb5de703a5d815df1db26de2291b", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_dependency_injection_basics", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}