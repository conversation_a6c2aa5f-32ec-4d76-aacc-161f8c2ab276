{"name": "Test lifecycle manager cleanup", "status": "passed", "description": "Test lifecycle manager cleanup functionality.", "start": 1751105429245, "stop": 1751105429252, "uuid": "eb1783ed-6689-4932-86e9-16fda98a3114", "historyId": "1e12c6cbda6118d11ab839200435dcad", "testCaseId": "1e12c6cbda6118d11ab839200435dcad", "fullName": "tests.container.test_containers_comprehensive.TestManagedPostgresSessionManager#test_lifecycle_manager_cleanup", "labels": [{"name": "story", "value": "Managed Database Sessions"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}