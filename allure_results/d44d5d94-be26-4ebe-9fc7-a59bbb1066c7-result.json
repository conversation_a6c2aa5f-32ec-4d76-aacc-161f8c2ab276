{"name": "Test PostgresSessionManager initialization", "status": "passed", "description": "Test PostgresSessionManager initialization.", "start": 1751105548298, "stop": 1751105548305, "uuid": "f44a6036-046a-472b-a777-495bfc6f7c4b", "historyId": "dbbfb647d41e9be86451bc17d071dcf3", "testCaseId": "dbbfb647d41e9be86451bc17d071dcf3", "fullName": "tests.container.test_containers_comprehensive.TestPostgresSessionManager#test_postgres_session_manager_init", "labels": [{"name": "feature", "value": "PostgresSessionManager"}, {"name": "story", "value": "Database Session Management"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}