{"name": "Test pg_ro_entry resolution", "status": "passed", "description": "Test that `pg_ro_entry` resolves correctly.", "start": 1751105409435, "stop": 1751105409436, "uuid": "3f8ea4fe-fe22-46b7-8428-12ee4e1dfd2d", "historyId": "591a87a01cbf5ec956bb0a8dd962a204", "testCaseId": "591a87a01cbf5ec956bb0a8dd962a204", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_pg_ro_entry", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}