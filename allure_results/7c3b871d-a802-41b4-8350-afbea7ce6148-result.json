{"name": "test_failed_fetch_returns_exception", "status": "passed", "description": "C:\\Users\\<USER>\\PycharmProjects\\airflow\\tests\\features\\fetch_with_retries.feature: Failed fetch returns exception", "attachments": [{"name": "stdout", "source": "c9569146-8d04-42a3-b5d9-76506ba8898a-attachment.txt", "type": "text/plain"}], "start": 1751105432067, "stop": 1751105432073, "uuid": "aa93271d-77b0-4cb4-9fed-9f0cb2e944ba", "historyId": "a882191d61a39582b9f699bc1ec128e0", "testCaseId": "a882191d61a39582b9f699bc1ec128e0", "fullName": "tests.steps.test_fetch_steps#test_failed_fetch_returns_exception", "labels": [{"name": "parentSuite", "value": "tests.steps"}, {"name": "suite", "value": "test_fetch_steps"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.steps.test_fetch_steps"}]}