{"name": "Test asyncpg driver availability", "status": "passed", "description": "Test that asyncpg driver is available and importable.", "attachments": [{"name": "stdout", "source": "18954efe-405e-4d43-a7bc-19c0bf8199ec-attachment.txt", "type": "text/plain"}], "start": 1751105429892, "stop": 1751105429894, "uuid": "84a97ad0-c12f-4d88-af34-3b634c33eb8a", "historyId": "5d097559dfa9b966cd74a345e3a31e2e", "testCaseId": "5d097559dfa9b966cd74a345e3a31e2e", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_asyncpg_available", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}