{"name": "Test database_rw session manager resolution", "status": "passed", "description": "Test that `database_rw` resolves correctly.", "start": 1751105409439, "stop": 1751105409439, "uuid": "790688e8-ccd5-4d8c-b843-c74aee6da5fd", "historyId": "c554717a9a077d7e4fe9963832f9a95d", "testCaseId": "c554717a9a077d7e4fe9963832f9a95d", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_database_rw", "labels": [{"name": "severity", "value": "minor"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}