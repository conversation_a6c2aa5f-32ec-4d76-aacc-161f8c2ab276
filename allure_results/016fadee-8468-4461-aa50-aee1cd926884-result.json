{"name": "Test auto-registration with lifecycle manager", "status": "passed", "description": "Test that ManagedPostgresSessionManager auto-registers with lifecycle manager.", "start": 1751105548334, "stop": 1751105548341, "uuid": "bfea4239-1874-411a-8fea-bea10cb8d464", "historyId": "dd85007d2530cac89b27f511e45a4cf2", "testCaseId": "dd85007d2530cac89b27f511e45a4cf2", "fullName": "tests.container.test_containers_comprehensive.TestManagedPostgresSessionManager#test_auto_registration", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Managed Database Sessions"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}