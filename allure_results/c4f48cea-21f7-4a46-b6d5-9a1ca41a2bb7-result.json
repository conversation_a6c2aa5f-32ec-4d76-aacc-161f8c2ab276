{"name": "test_dependency_injection_pattern", "status": "passed", "description": "Test dependency injection pattern with logger.", "attachments": [{"name": "stdout", "source": "dd75dc97-c2ee-4d5d-80f9-e07f7ba4d96b-attachment.txt", "type": "text/plain"}], "start": 1751105436048, "stop": 1751105436050, "uuid": "caf44dc1-c024-4c56-9467-e6c3900b9243", "historyId": "313a8f97e7c095b791febdb0bde3826d", "testCaseId": "313a8f97e7c095b791febdb0bde3826d", "fullName": "tests.test_logger_refactor#test_dependency_injection_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}