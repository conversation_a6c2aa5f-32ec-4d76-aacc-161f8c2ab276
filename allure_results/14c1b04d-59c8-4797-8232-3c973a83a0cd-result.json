{"name": "Test process_jira_issues with mocked services", "status": "passed", "description": "Test process_jira_issues function with mocked external services", "attachments": [{"name": "log", "source": "fbb33842-1958-4522-ac81-3bae62359409-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "5113a9d8-668e-4eea-88fa-697888b55ac6-attachment.txt", "type": "text/plain"}], "start": 1751105563341, "stop": 1751105564604, "uuid": "15ed734a-11bb-483f-b620-ff72e33d79b9", "historyId": "57c0a3912d550bd0e41d39a4e4dee4f2", "testCaseId": "57c0a3912d550bd0e41d39a4e4dee4f2", "fullName": "tests.test_jira_api_mocks.TestJiraIssues#test_process_jira_issues", "labels": [{"name": "story", "value": "Process Jira Issues"}, {"name": "feature", "value": "Jira API"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraIssues"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}