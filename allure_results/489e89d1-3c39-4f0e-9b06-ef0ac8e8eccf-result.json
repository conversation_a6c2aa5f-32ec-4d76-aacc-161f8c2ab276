{"name": "Test queue selection for \"plat\" schema", "status": "passed", "description": "Test that the 'plat' schema returns the correct queue instances.", "start": 1751105548115, "stop": 1751105548116, "uuid": "b761e7eb-005a-4e79-b454-1408e1d4cad8", "historyId": "12b2ff5229c222155457cead0b9aecde", "testCaseId": "12b2ff5229c222155457cead0b9aecde", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_queue_selector_plat", "labels": [{"name": "story", "value": "Test queue selection for different schemas"}, {"name": "epic", "value": "Queue Handling"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/plat-schema", "name": "http://testcase-link.com/plat-schema"}]}