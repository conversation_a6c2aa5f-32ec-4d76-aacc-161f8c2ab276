{"name": "<PERSON>", "status": "passed", "description": "\n        Test that the container initializes correctly.\n        All schema exists\n        ", "start": 1751105421367, "stop": 1751105424541, "uuid": "38cf52bb-eebb-4d3a-b967-3d8fe8bb2cd1", "historyId": "e1f1c70ca6e9749125f9bf03610fc230", "testCaseId": "e1f1c70ca6e9749125f9bf03610fc230", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_container_initialization", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test against real db"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}], "links": [{"type": "issue", "url": "https://biyani.atlassian.net/browse/AIR-47", "name": "test issue linkage"}, {"type": "issue", "url": "https://biyani.atlassian.net/browse/AIR-47", "name": "create schema"}]}