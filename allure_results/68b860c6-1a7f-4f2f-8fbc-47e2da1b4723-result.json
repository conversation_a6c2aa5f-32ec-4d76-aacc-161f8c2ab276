{"name": "test_successful_fetch_returns_result", "status": "passed", "description": "C:\\Users\\<USER>\\PycharmProjects\\airflow\\tests\\features\\fetch_with_retries.feature: Successful fetch returns result", "attachments": [{"name": "stdout", "source": "610d6d69-4f21-48d4-9d94-0a69a3e4433d-attachment.txt", "type": "text/plain"}], "start": 1751105432044, "stop": 1751105432051, "uuid": "d32f143f-ce87-43c2-a3df-278b778e446f", "historyId": "30881ec731ea11d5f28f7606404380db", "testCaseId": "30881ec731ea11d5f28f7606404380db", "fullName": "tests.steps.test_fetch_steps#test_successful_fetch_returns_result", "labels": [{"name": "parentSuite", "value": "tests.steps"}, {"name": "suite", "value": "test_fetch_steps"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.steps.test_fetch_steps"}]}