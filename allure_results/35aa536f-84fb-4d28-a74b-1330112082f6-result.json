{"name": "Test JiraEntryDetailsContainer Singleton behavior", "status": "passed", "description": "Test that the `jira_entry_details` is resolved as a singleton.", "start": 1751105425740, "stop": 1751105425743, "uuid": "70fa3439-85d7-4946-ae2c-f74ead33efac", "historyId": "b294e07c534a2d66401f773b4f4d4c5f", "testCaseId": "b294e07c534a2d66401f773b4f4d4c5f", "fullName": "tests.container.jira_entry_details.test_jira_entry_details.TestJiraEntryDetailsContainer#test_jira_entry_details_singleton", "labels": [{"name": "feature", "value": "JiraEntryDetailsContainer"}, {"name": "severity", "value": "minor"}, {"name": "story", "value": "Test Jira entry details resolution"}, {"name": "parentSuite", "value": "tests.container.jira_entry_details"}, {"name": "suite", "value": "test_jira_entry_details"}, {"name": "subSuite", "value": "TestJiraEntryDetailsContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.jira_entry_details.test_jira_entry_details"}]}