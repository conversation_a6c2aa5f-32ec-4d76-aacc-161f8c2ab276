{"name": "Test pg_rw_entry resolution", "status": "passed", "description": "Test that `pg_rw_entry` resolves correctly.", "start": 1751105531224, "stop": 1751105532541, "uuid": "aaf95310-1932-405a-86ce-cd14e729b8f7", "historyId": "e0b66914c26711b12e1f363b6de80dd5", "testCaseId": "e0b66914c26711b12e1f363b6de80dd5", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_rw_entry", "labels": [{"name": "story", "value": "Test against real db"}, {"name": "requirement", "value": "AIR-1"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}], "links": [{"type": "issue", "url": "AIR-41", "name": "View issue in Jira"}]}