{"name": "test_driver_versions", "status": "passed", "description": "Standalone function to test and print driver versions.", "attachments": [{"name": "stdout", "source": "0137e0d1-4ea6-4de7-bb5b-98ea4b800945-attachment.txt", "type": "text/plain"}], "start": 1751105549621, "stop": 1751105549626, "uuid": "7fd5fb34-182e-468a-89c2-dfebb42d6c93", "historyId": "6e98db8f9a480ca356c852c970f08706", "testCaseId": "6e98db8f9a480ca356c852c970f08706", "fullName": "tests.container.test_database_drivers#test_driver_versions", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}