{"name": "Test database_ro session manager resolution", "status": "passed", "description": "Test that `database_ro` resolves correctly.", "attachments": [{"name": "stdout", "source": "37f79f5c-2ad1-426c-ab07-1cce79edac3b-attachment.txt", "type": "text/plain"}], "start": 1751105409443, "stop": 1751105409444, "uuid": "a3a1de14-68fb-476f-b46b-ac3d7cd67c2a", "historyId": "3c40acb320722585ee2023d2b5390a1c", "testCaseId": "3c40acb320722585ee2023d2b5390a1c", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_database_ro", "labels": [{"name": "severity", "value": "minor"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}