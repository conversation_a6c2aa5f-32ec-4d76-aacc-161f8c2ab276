{"name": "test_psycopg3_conflict", "status": "passed", "description": "Test if psycopg3 is installed (should not be).", "attachments": [{"name": "stdout", "source": "f09a212c-8777-4cb5-b74c-7a0bb434d1c1-attachment.txt", "type": "text/plain"}], "start": 1751105429141, "stop": 1751105429143, "uuid": "815994bb-0311-458c-b59c-86393a93c96b", "historyId": "ba4f6d37eb420a3ca20b332c62b9a5e6", "testCaseId": "ba4f6d37eb420a3ca20b332c62b9a5e6", "fullName": "tests.container.quick_test#test_psycopg3_conflict", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "quick_test"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.quick_test"}]}