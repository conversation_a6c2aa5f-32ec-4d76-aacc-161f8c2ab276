{"name": "Test basic imports", "status": "passed", "description": "Test that basic required modules can be imported.", "attachments": [{"name": "stdout", "source": "cb227107-c872-4724-ad8e-d79b3f2c4816-attachment.txt", "type": "text/plain"}], "start": 1751105548162, "stop": 1751105548163, "uuid": "be2e1dc1-e0d5-426b-8ada-1150a2affead", "historyId": "5bccab9908afef60fcc1a90e8d1aa589", "testCaseId": "5bccab9908afef60fcc1a90e8d1aa589", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_basic_imports", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "blocker"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}