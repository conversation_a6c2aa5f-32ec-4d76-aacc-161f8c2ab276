{"name": "Test lifecycle manager provider", "status": "passed", "description": "Test lifecycle manager provider.", "start": 1751105549221, "stop": 1751105549439, "uuid": "0b56cfaf-c66d-4eb2-b753-362a10d78ac3", "historyId": "f4f8ba0f84fae9fa606bec72097d0ce3", "testCaseId": "f4f8ba0f84fae9fa606bec72097d0ce3", "fullName": "tests.container.test_containers_comprehensive.TestApplicationContainer#test_lifecycle_manager_provider", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}