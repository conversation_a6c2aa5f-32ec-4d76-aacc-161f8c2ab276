{"name": "Test psycopg2 driver availability", "status": "passed", "description": "Test that psycopg2 driver is available and importable.", "attachments": [{"name": "stdout", "source": "fa8ddb98-2760-40c0-a414-ae6f7d914041-attachment.txt", "type": "text/plain"}], "start": 1751105429884, "stop": 1751105429884, "uuid": "5a8460f8-6a67-45be-92e0-a11d4f41c034", "historyId": "f22da130b98ff04199cec12f474d4841", "testCaseId": "f22da130b98ff04199cec12f474d4841", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_psycopg2_available", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}