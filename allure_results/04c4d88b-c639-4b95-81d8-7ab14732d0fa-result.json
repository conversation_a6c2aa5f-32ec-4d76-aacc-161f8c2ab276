{"name": "test_fetch_with_retries_failure", "status": "passed", "description": "Test that failed responses have consistent format", "attachments": [{"name": "stdout", "source": "8e07cf8b-ed48-4b62-bd1c-85a019bc1ec3-attachment.txt", "type": "text/plain"}], "start": 1751105432103, "stop": 1751105432106, "uuid": "d49f15d9-c5da-4174-859a-815263fc3799", "historyId": "df34c252c1894c3c42fa82c6dc510698", "testCaseId": "df34c252c1894c3c42fa82c6dc510698", "fullName": "tests.test_fetch_with_retries_consistency#test_fetch_with_retries_failure", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}