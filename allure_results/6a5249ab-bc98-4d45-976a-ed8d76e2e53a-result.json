{"name": "Test singleton behavior of <PERSON><PERSON><PERSON><PERSON> manager", "status": "passed", "description": "Test that `keepass_manager` is resolved as a singleton.", "start": 1751105531016, "stop": 1751105531016, "uuid": "3cb41888-0311-47e1-95e7-e42ec00b65f2", "historyId": "19f6992cf17fe9b880567d6abec05931", "testCaseId": "19f6992cf17fe9b880567d6abec05931", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_keepass_manager_singleton", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}