{"name": "Test get_kp_entry_details resolution for PG_RO", "status": "passed", "description": "Test that the `pg_ro` resolves correctly with the appropriate title.", "start": 1751105429012, "stop": 1751105429016, "uuid": "058d9810-163c-4bfe-bf05-6a44c7613127", "historyId": "9d7d858dd056a153d91441cb5ce22601", "testCaseId": "9d7d858dd056a153d91441cb5ce22601", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_pg_ro_entry_details", "labels": [{"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}