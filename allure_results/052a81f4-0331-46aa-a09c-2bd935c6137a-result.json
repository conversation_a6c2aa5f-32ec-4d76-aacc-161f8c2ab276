{"name": "Test get_kp_entry_details resolution for JIRA_ENTRY", "status": "passed", "description": "Test that the `jira_entry_details` resolves correctly with the appropriate title.", "start": 1751105547869, "stop": 1751105547874, "uuid": "56913dc1-c012-4529-bd12-97c4cacdd995", "historyId": "84d86013e4943561b2b41faa63cb8e56", "testCaseId": "84d86013e4943561b2b41faa63cb8e56", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_jira_entry_details", "labels": [{"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}