{"name": "Test EntryDetails string representation", "status": "passed", "description": "Test string representation hides password.", "start": 1751105548289, "stop": 1751105548289, "uuid": "e2789a64-d13b-4a62-aa0a-a7e6dab8cf72", "historyId": "ffd3c1895820e4f5727d838e33b537e6", "testCaseId": "ffd3c1895820e4f5727d838e33b537e6", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_repr", "labels": [{"name": "story", "value": "Entry Details Management"}, {"name": "feature", "value": "EntryDetails"}, {"name": "severity", "value": "minor"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}