{"name": "Test get_jira_users with mocked services", "status": "passed", "description": "Test get_jira_users with properly mocked dependencies using dependency injection", "attachments": [{"name": "stdout", "source": "8d03967d-c952-4b8b-81aa-f96539e8a3dc-attachment.txt", "type": "text/plain"}], "start": 1751105551909, "stop": 1751105552834, "uuid": "288af25e-295e-47c8-929a-b26e87aa3054", "historyId": "0fc556b318c393981f356e565c5e82b3", "testCaseId": "0fc556b318c393981f356e565c5e82b3", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}