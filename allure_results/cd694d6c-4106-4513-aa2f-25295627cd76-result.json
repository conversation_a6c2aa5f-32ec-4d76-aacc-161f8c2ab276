{"name": "Test schema override", "status": "passed", "description": "Test schema override functionality.", "start": 1751105548597, "stop": 1751105548794, "uuid": "0cda9e74-3b88-40d8-9600-0fda5829627e", "historyId": "f6c53843ff850bae44f17635a877c665", "testCaseId": "f6c53843ff850bae44f17635a877c665", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_schema_override", "labels": [{"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}