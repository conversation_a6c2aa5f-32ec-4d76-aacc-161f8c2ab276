{"name": "Test pg_ro_entry resolution", "status": "passed", "description": "Test that `pg_ro_entry` resolves correctly.", "start": 1751105409564, "stop": 1751105409565, "uuid": "c4c038be-35f6-47cf-9437-eefc5d052866", "historyId": "8a4b8e300fa891b783be5f81f170baf4", "testCaseId": "8a4b8e300fa891b783be5f81f170baf4", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_pg_ro_entry", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}