{"name": "test_configuration_pattern", "status": "passed", "description": "Test configuration management pattern.", "attachments": [{"name": "stdout", "source": "ab68a4e8-6483-480d-a5a6-cd7b642a4558-attachment.txt", "type": "text/plain"}], "start": 1751105564747, "stop": 1751105564749, "uuid": "24be5fba-bbb2-4bad-a67e-9251d2e82ac2", "historyId": "1df96e89cb68c22ef905bab9d6a5eb2a", "testCaseId": "1df96e89cb68c22ef905bab9d6a5eb2a", "fullName": "tests.test_logger_refactor#test_configuration_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}