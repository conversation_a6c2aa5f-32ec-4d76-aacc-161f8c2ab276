{"name": "Test Jira entry details resolution for JIRA_ENTRY", "status": "passed", "description": "Test that the `jira_entry_details` resolves correctly with the expected username and URL.", "start": 1751105425727, "stop": 1751105425732, "uuid": "cbb5b04f-d297-42e1-a739-afd1ca837143", "historyId": "f79231333c85f303d30dacb866300a48", "testCaseId": "f79231333c85f303d30dacb866300a48", "fullName": "tests.container.jira_entry_details.test_jira_entry_details.TestJiraEntryDetailsContainer#test_jira_entry_details", "labels": [{"name": "feature", "value": "JiraEntryDetailsContainer"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Test Jira entry details resolution"}, {"name": "parentSuite", "value": "tests.container.jira_entry_details"}, {"name": "suite", "value": "test_jira_entry_details"}, {"name": "subSuite", "value": "TestJiraEntryDetailsContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.jira_entry_details.test_jira_entry_details"}]}