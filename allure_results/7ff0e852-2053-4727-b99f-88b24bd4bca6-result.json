{"name": "Test asyncpg driver availability", "status": "passed", "description": "Test that asyncpg driver is available and importable.", "attachments": [{"name": "stdout", "source": "870a02c3-c172-47f6-9a47-b5df0d51f9d2-attachment.txt", "type": "text/plain"}], "start": 1751105549551, "stop": 1751105549552, "uuid": "337af1a8-504b-4f48-96c1-16a8ba820e28", "historyId": "5d097559dfa9b966cd74a345e3a31e2e", "testCaseId": "5d097559dfa9b966cd74a345e3a31e2e", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_asyncpg_available", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Database Drivers"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}