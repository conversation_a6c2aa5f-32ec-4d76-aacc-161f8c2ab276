{"name": "Test build_entry_details with missing entry", "status": "passed", "description": "Test build_entry_details when entry is not found.", "start": 1751105429837, "stop": 1751105429839, "uuid": "69546980-316c-4ac4-b951-a6d4e237c434", "historyId": "6f6b1abba38909e11586d1db92274581", "testCaseId": "6f6b1abba38909e11586d1db92274581", "fullName": "tests.container.test_containers_comprehensive.TestErrorHandling#test_build_entry_details_missing_entry", "labels": [{"name": "story", "value": "Exception Handling and Edge Cases"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Erro<PERSON>"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestError<PERSON><PERSON>ling"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}