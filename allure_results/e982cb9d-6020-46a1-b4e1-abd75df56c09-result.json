{"name": "Test EntryDetailsBuilder pattern", "status": "passed", "description": "Test the builder pattern for EntryDetails.", "start": 1751105548281, "stop": 1751105548281, "uuid": "bdcc1105-203c-41f6-ac49-16a78e9e671a", "historyId": "15084eed0a800e1f870b7019359a9d45", "testCaseId": "15084eed0a800e1f870b7019359a9d45", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_builder", "labels": [{"name": "story", "value": "Entry Details Management"}, {"name": "feature", "value": "EntryDetails"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}