{"name": "Test database provider resolution", "status": "passed", "description": "Test that database providers resolve correctly.", "start": 1751105548805, "stop": 1751105549032, "uuid": "f836dc2b-b69f-4af1-99e6-820dfdc5fbdd", "historyId": "979f2ad82379efb258a9a431b7ed212b", "testCaseId": "979f2ad82379efb258a9a431b7ed212b", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_database_provider_resolution", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}