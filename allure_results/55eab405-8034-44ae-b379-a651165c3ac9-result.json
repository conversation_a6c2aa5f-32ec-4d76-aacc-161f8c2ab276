{"name": "Test database driver imports", "status": "passed", "description": "Test that database drivers can be imported.", "attachments": [{"name": "stdout", "source": "ce54a834-1cbc-496b-8cb7-31854536f471-attachment.txt", "type": "text/plain"}], "start": 1751105548172, "stop": 1751105548173, "uuid": "63b6867c-082c-4af2-94f1-add43bcb0026", "historyId": "774c3dcbd1522e2d23647c68ce8e8171", "testCaseId": "774c3dcbd1522e2d23647c68ce8e8171", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_database_driver_imports", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "blocker"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}