{"name": "Test schema override", "status": "passed", "description": "Test schema override functionality.", "start": 1751105429351, "stop": 1751105429430, "uuid": "d1e8ac64-a468-4cec-b336-e5b32fe1b303", "historyId": "f6c53843ff850bae44f17635a877c665", "testCaseId": "f6c53843ff850bae44f17635a877c665", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_schema_override", "labels": [{"name": "story", "value": "Container Dependency Injection"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}