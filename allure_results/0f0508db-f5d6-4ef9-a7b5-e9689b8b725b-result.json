{"name": "Test database_rw session manager resolution", "status": "passed", "description": "Test that `database_rw` resolves correctly.", "start": 1751105531110, "stop": 1751105531112, "uuid": "f787f3ec-f75b-4901-845f-dd2244fbba9a", "historyId": "81d802e095fc0799cd09239d0c80c39b", "testCaseId": "81d802e095fc0799cd09239d0c80c39b", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_database_rw", "labels": [{"name": "severity", "value": "minor"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}