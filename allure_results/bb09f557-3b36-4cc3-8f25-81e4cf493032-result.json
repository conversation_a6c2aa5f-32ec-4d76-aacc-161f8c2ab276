{"name": "Test EntryDetails creation", "status": "passed", "description": "Test creating EntryDetails with all fields.", "start": 1751105429208, "stop": 1751105429209, "uuid": "*************-4df8-a945-1df551381f32", "historyId": "488fc987e6c1e22c41d4a0d87be8b625", "testCaseId": "488fc987e6c1e22c41d4a0d87be8b625", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_creation", "labels": [{"name": "feature", "value": "EntryDetails"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Entry Details Management"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}