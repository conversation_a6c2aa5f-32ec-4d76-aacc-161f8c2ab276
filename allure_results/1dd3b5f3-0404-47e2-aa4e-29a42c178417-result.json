{"name": "Test database_lifecycle context manager", "status": "passed", "description": "Test database_lifecycle async context manager.", "start": 1751105429777, "stop": 1751105429779, "uuid": "51431f93-26cb-4dca-8c21-972b551e109a", "historyId": "94d812df689755263e6be08f9319f217", "testCaseId": "94d812df689755263e6be08f9319f217", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_database_lifecycle_context_manager", "labels": [{"name": "story", "value": "Helper Functions and Decorators"}, {"name": "feature", "value": "Utility Functions"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}