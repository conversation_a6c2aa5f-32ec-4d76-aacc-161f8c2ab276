{"name": "Test dataclass creation", "status": "passed", "description": "Test that dataclasses work correctly.", "attachments": [{"name": "stdout", "source": "d3b70b3f-b65e-449b-adee-1f90d395fde3-attachment.txt", "type": "text/plain"}], "start": 1751105429173, "stop": 1751105429175, "uuid": "8b3c815f-41fe-43d4-9bdc-6e5b465d1271", "historyId": "df7a5bfac4fe527d868c9ceee9c21b78", "testCaseId": "df7a5bfac4fe527d868c9ceee9c21b78", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_dataclass_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}