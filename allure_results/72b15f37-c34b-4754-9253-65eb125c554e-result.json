{"name": "Test container initialization", "status": "passed", "description": "Test DatabaseSessionManagerContainer initialization.", "start": 1751105548382, "stop": 1751105548588, "uuid": "0ba3572f-d322-4ea6-9a3b-32a3ce8348a6", "historyId": "47fb875795c838bf2b5243f61d43b93d", "testCaseId": "47fb875795c838bf2b5243f61d43b93d", "fullName": "tests.container.test_containers_comprehensive.TestDatabaseSessionManagerContainer#test_container_initialization", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "DatabaseSessionManagerContainer"}, {"name": "story", "value": "Container Dependency Injection"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestDatabaseSessionManagerContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}