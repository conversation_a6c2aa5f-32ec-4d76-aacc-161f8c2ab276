{"name": "Test database_lifecycle context manager", "status": "passed", "description": "Test database_lifecycle async context manager.", "start": 1751105549467, "stop": 1751105549470, "uuid": "cdee3591-9907-4e9d-8e56-914eb555061e", "historyId": "94d812df689755263e6be08f9319f217", "testCaseId": "94d812df689755263e6be08f9319f217", "fullName": "tests.container.test_containers_comprehensive.TestUtilityFunctions#test_database_lifecycle_context_manager", "labels": [{"name": "feature", "value": "Utility Functions"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Helper Functions and Decorators"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestUtilityFunctions"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}