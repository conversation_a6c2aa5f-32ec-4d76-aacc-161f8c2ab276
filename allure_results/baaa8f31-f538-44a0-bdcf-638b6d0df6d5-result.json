{"name": "test_fetch_with_retries_success", "status": "passed", "description": "Test that successful responses have consistent format", "attachments": [{"name": "stdout", "source": "17ffc545-7247-4de6-8844-781f2cf9e204-attachment.txt", "type": "text/plain"}], "start": 1751105551829, "stop": 1751105551835, "uuid": "cc770743-4378-4c84-a99e-5e37876511c9", "historyId": "c37f1a4437e742ab070ba08b2ee81145", "testCaseId": "c37f1a4437e742ab070ba08b2ee81145", "fullName": "tests.test_fetch_with_retries_consistency#test_fetch_with_retries_success", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}