{"name": "test_dependency_injection_pattern", "status": "passed", "description": "Test dependency injection pattern with logger.", "attachments": [{"name": "stdout", "source": "f316ab0f-0c8f-4ac9-9979-cc658f202f32-attachment.txt", "type": "text/plain"}], "start": 1751105564733, "stop": 1751105564737, "uuid": "eae5c3a5-34a9-4425-9b08-222b1727dc72", "historyId": "313a8f97e7c095b791febdb0bde3826d", "testCaseId": "313a8f97e7c095b791febdb0bde3826d", "fullName": "tests.test_logger_refactor#test_dependency_injection_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}