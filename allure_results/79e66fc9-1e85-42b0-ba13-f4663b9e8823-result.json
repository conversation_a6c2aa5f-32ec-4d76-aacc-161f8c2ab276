{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Extract field IDs for datatype 'number'", "status": "passed", "start": 1751105543669, "stop": 1751105543669}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751105543669, "stop": 1751105543669}], "parameters": [{"name": "datatype", "value": "'number'"}, {"name": "expected_ids", "value": "['aggregatetimeestimate', 'aggregatetimeoriginalestimate', 'aggregatetimespent', 'customfield_10024', 'customfield_10120', 'customfield_10121', 'customfield_10122', 'customfield_10123', 'customfield_10124', 'customfield_10125', 'customfield_10126', 'customfield_10147', 'customfield_10199', 'timeestimate', 'timeoriginalestimate', 'timespent']"}], "start": 1751105543669, "stop": 1751105543669, "uuid": "49aba7f2-5303-434a-99c8-3706e4856dd8", "historyId": "85ab4147f0ef91e90d779b6caf4bf4d5", "testCaseId": "a22222b2720a766a127ad4fd3b149427", "fullName": "tests.container.issue_fields_container.test_field_name_extractor.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_field_name_extractor"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_field_name_extractor"}]}