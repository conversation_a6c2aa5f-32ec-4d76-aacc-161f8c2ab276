{"name": "test_driver_versions", "status": "passed", "description": "Standalone function to test and print driver versions.", "attachments": [{"name": "stdout", "source": "bc443218-a550-41dd-865a-71dec7d31a62-attachment.txt", "type": "text/plain"}], "start": 1751105429970, "stop": 1751105429972, "uuid": "9f774764-91d1-4995-bd56-bc0d77c384b7", "historyId": "6e98db8f9a480ca356c852c970f08706", "testCaseId": "6e98db8f9a480ca356c852c970f08706", "fullName": "tests.container.test_database_drivers#test_driver_versions", "labels": [{"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}