{"name": "Test singleton behavior of <PERSON><PERSON><PERSON><PERSON> manager", "status": "passed", "description": "Test that `keepass_manager` is resolved as a singleton.", "start": 1751105409583, "stop": 1751105409583, "uuid": "f22792ca-ee9b-4097-aa28-df019e7eb708", "historyId": "a476b6ffafe8b5526f25f882b40d03db", "testCaseId": "a476b6ffafe8b5526f25f882b40d03db", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_keepass_manager_singleton", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "story", "value": "Test component resolutions"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}