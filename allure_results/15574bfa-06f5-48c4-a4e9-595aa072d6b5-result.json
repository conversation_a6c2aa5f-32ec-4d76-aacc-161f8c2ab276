{"name": "Test pg_ro_entry resolution with different schema", "status": "passed", "description": "Test that `pg_ro_entry` resolves with a different schema.", "start": 1751105418371, "stop": 1751105419772, "uuid": "34fa98e9-4389-4a9e-901c-9573a753d4d0", "historyId": "c8385bdffc6c4da80da5dafadb94e832", "testCaseId": "c8385bdffc6c4da80da5dafadb94e832", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_ro_entry_different_schema", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Test against real db"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}