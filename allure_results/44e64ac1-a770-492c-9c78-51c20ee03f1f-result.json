{"name": "Test mocked engine creation", "status": "passed", "description": "Test engine creation with mocked SQLAlchemy.", "attachments": [{"name": "stdout", "source": "ef9dd1a2-f313-49b5-afd0-bfa032f882c6-attachment.txt", "type": "text/plain"}], "start": 1751105548195, "stop": 1751105548202, "uuid": "2b160bf7-4350-484c-a8fc-b982f86d6304", "historyId": "ab8f7500848a585d8b922a2ed42d4557", "testCaseId": "ab8f7500848a585d8b922a2ed42d4557", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_mocked_engine_creation", "labels": [{"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}