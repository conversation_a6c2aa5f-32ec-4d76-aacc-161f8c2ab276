{"name": "test_configuration_pattern", "status": "passed", "description": "Test configuration management pattern.", "attachments": [{"name": "stdout", "source": "17e08851-6b06-4920-9f86-9abc72ca5785-attachment.txt", "type": "text/plain"}], "start": 1751105436055, "stop": 1751105436056, "uuid": "ed12b027-617a-4fcc-825b-dc1d9c46921e", "historyId": "1df96e89cb68c22ef905bab9d6a5eb2a", "testCaseId": "1df96e89cb68c22ef905bab9d6a5eb2a", "fullName": "tests.test_logger_refactor#test_configuration_pattern", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}