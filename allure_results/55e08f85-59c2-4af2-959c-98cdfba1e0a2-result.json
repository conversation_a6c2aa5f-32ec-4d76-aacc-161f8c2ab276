{"name": "Test pg_ro_entry resolution", "status": "passed", "description": "Test that `pg_ro_entry` resolves correctly.", "start": 1751105531105, "stop": 1751105531105, "uuid": "a12df06a-d702-4eb4-91f6-462970aca562", "historyId": "8a4b8e300fa891b783be5f81f170baf4", "testCaseId": "8a4b8e300fa891b783be5f81f170baf4", "fullName": "tests.container.application_container.test_application_container_mock.TestApplicationContainer#test_pg_ro_entry", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_mock"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_mock"}]}