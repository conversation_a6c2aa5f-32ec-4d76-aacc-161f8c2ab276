{"name": "Test SQLAlchemy URL creation with psycopg2", "status": "passed", "description": "Test SQLAlchemy URL creation with psycopg2 driver.", "attachments": [{"name": "stdout", "source": "c3cda1c5-**************-948567a6bace-attachment.txt", "type": "text/plain"}], "start": 1751105429911, "stop": 1751105429911, "uuid": "ca3a590b-6657-4b3c-91e5-1fa20452376f", "historyId": "bcce6ca9b61a783705914930f8b24c03", "testCaseId": "bcce6ca9b61a783705914930f8b24c03", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_url_psycopg2", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}