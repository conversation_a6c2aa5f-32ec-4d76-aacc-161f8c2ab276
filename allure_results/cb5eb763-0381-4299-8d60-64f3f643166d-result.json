{"name": "Test EntryDetails string representation", "status": "passed", "description": "Test string representation hides password.", "start": 1751105429217, "stop": 1751105429217, "uuid": "cb87a962-0e62-4c04-9074-e2269e61ffde", "historyId": "ffd3c1895820e4f5727d838e33b537e6", "testCaseId": "ffd3c1895820e4f5727d838e33b537e6", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_repr", "labels": [{"name": "severity", "value": "minor"}, {"name": "feature", "value": "EntryDetails"}, {"name": "story", "value": "Entry Details Management"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}