{"name": "Test schema_ro entry details", "status": "passed", "description": "Test that the schema_ro resolves correctly with the appropriate title.", "start": 1751105429029, "stop": 1751105429035, "uuid": "02cc8de7-0982-48a2-b85e-2c78ff0c677a", "historyId": "7e25073868f414a89013281fd1a875ba", "testCaseId": "7e25073868f414a89013281fd1a875ba", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_schema_ro_entry_details", "labels": [{"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}