{"name": "Test queue selection for \"plat\" schema", "status": "passed", "description": "Test that the 'plat' schema returns the correct queue instances.", "start": 1751105429119, "stop": 1751105429120, "uuid": "a7e1b405-232f-43ee-a0f6-cbbe76fcd542", "historyId": "12b2ff5229c222155457cead0b9aecde", "testCaseId": "12b2ff5229c222155457cead0b9aecde", "fullName": "tests.container.queue_container.test_queue_container.TestQueueContainer#test_queue_selector_plat", "labels": [{"name": "severity", "value": "normal"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON>"}, {"name": "epic", "value": "Queue Handling"}, {"name": "feature", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "suite", "value": "Queue Selection Tests"}, {"name": "story", "value": "Test queue selection for different schemas"}, {"name": "parentSuite", "value": "tests.container.queue_container"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.queue_container.test_queue_container"}], "links": [{"type": "tms", "url": "http://testcase-link.com/plat-schema", "name": "http://testcase-link.com/plat-schema"}]}