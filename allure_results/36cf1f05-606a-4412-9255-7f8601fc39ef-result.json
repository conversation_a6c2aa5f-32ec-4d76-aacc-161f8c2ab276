{"name": "Test schema_translate_map update to different schema", "status": "passed", "description": "Test that `update_schema` correctly updates the schema_translate_map on the engines.", "start": 1751105538302, "stop": 1751105541741, "uuid": "6ee7ea00-1a23-44cd-b487-a7f2bb1aea32", "historyId": "00d7e3ca7cc4bd50d4907d070b1bfcac", "testCaseId": "00d7e3ca7cc4bd50d4907d070b1bfcac", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_update_schema_updates_schema_translate_map", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test against real db"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}