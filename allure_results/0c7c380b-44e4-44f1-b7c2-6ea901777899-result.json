{"name": "Test sync function success after retries", "status": "passed", "description": "Tests that the sync function successfully completes after retrying due to deadlocks.", "attachments": [{"name": "Sync Retry Attempts", "source": "ea5d328e-d80f-493c-9d0c-d70559c055ea-attachment.txt", "type": "text/plain"}], "start": 1751105429986, "stop": 1751105430389, "uuid": "829ecec0-6200-47ab-a2d0-77f65ce16f01", "historyId": "d4e645d86e3313fc57a0afc12ceb0a92", "testCaseId": "d4e645d86e3313fc57a0afc12ceb0a92", "fullName": "tests.smart_retry.test_smart_retry#test_sync_success", "labels": [{"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}