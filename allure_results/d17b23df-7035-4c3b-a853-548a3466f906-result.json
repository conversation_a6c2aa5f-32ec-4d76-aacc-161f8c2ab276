{"name": "Test pg_ro_entry resolution", "status": "passed", "description": "Test that `pg_ro_entry` resolves correctly.", "start": 1751105532632, "stop": 1751105533990, "uuid": "a19f8af4-cf36-459e-9452-3f5beb462808", "historyId": "2034b186413dbb8980186902dfa71797", "testCaseId": "2034b186413dbb8980186902dfa71797", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_ro_entry", "labels": [{"name": "story", "value": "Test against real db"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}