{"name": "Test async function success after retries", "status": "passed", "description": "Tests that the async function successfully completes after retrying due to deadlocks.", "attachments": [{"name": "Async Retry Attempts", "source": "6600359e-dbc1-456b-ae64-d8917db55260-attachment.txt", "type": "text/plain"}], "start": 1751105431000, "stop": 1751105431412, "uuid": "a38b3411-0d11-499c-81ce-8d6c2f1f2d85", "historyId": "5fa3a9df77cf7f9c814ef156472e7c41", "testCaseId": "5fa3a9df77cf7f9c814ef156472e7c41", "fullName": "tests.smart_retry.test_smart_retry#test_async_success", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "suite_smart_retry"}, {"name": "parentSuite", "value": "tests.smart_retry"}, {"name": "suite", "value": "test_smart_retry"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.smart_retry.test_smart_retry"}]}