{"name": "Test auto-registration with lifecycle manager", "status": "passed", "description": "Test that ManagedPostgresSessionManager auto-registers with lifecycle manager.", "start": 1751105429238, "stop": 1751105429240, "uuid": "f834b430-9ff5-40a4-aa17-fc809dba21e1", "historyId": "dd85007d2530cac89b27f511e45a4cf2", "testCaseId": "dd85007d2530cac89b27f511e45a4cf2", "fullName": "tests.container.test_containers_comprehensive.TestManagedPostgresSessionManager#test_auto_registration", "labels": [{"name": "story", "value": "Managed Database Sessions"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "ManagedPostgresSessionManager"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestManagedPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}