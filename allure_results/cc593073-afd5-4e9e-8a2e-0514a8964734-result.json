{"name": "Test singleton behavior of pg_rw_entry", "status": "passed", "description": "Test that `pg_rw_entry` is resolved as a singleton.", "start": 1751105535498, "stop": 1751105536824, "uuid": "ac24a224-e166-4076-9316-778e20dc302f", "historyId": "835e5a349672262d82bb03f6e1bbc58f", "testCaseId": "835e5a349672262d82bb03f6e1bbc58f", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_rw_entry_singleton", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test against real db"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}