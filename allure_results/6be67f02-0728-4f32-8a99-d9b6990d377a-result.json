{"name": "Test Kee<PERSON>ass<PERSON>ana<PERSON> behavior", "status": "passed", "description": "Test that the KeePassManager behaves as a singleton.", "start": 1751105547942, "stop": 1751105547943, "uuid": "78941a9d-bfa7-413c-aa85-4c6de543c5b8", "historyId": "35ea2e5cf3db6a79c98d634fafa1724f", "testCaseId": "35ea2e5cf3db6a79c98d634fafa1724f", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_keepass_manager_singleton", "labels": [{"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "minor"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}