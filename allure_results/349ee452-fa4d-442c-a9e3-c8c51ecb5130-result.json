{"name": "Test singleton behavior of pg_rw_entry", "status": "passed", "description": "Test that `pg_rw_entry` is resolved as a singleton.", "start": 1751105531020, "stop": 1751105531020, "uuid": "28b8cf44-d4df-4c0d-91e9-caadeb021260", "historyId": "061e205806d2cfa2d7643feab1f18397", "testCaseId": "061e205806d2cfa2d7643feab1f18397", "fullName": "tests.container.application_container.test_application_container_experiment.TestApplicationContainer#test_pg_rw_entry_singleton", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test component resolutions"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_experiment"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_experiment"}]}