{"name": "Test singleton behavior of pg_rw_entry", "status": "passed", "description": "Test that `pg_rw_entry` is resolved as a singleton.", "start": 1751105416485, "stop": 1751105418243, "uuid": "d26b5f4f-9ad3-4427-bf81-d7eba9e72fd2", "historyId": "835e5a349672262d82bb03f6e1bbc58f", "testCaseId": "835e5a349672262d82bb03f6e1bbc58f", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_rw_entry_singleton", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Test against real db"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}