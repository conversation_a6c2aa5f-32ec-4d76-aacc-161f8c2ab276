{"name": "Test PostgresSessionManager initialization", "status": "passed", "description": "Test PostgresSessionManager initialization.", "start": 1751105429220, "stop": 1751105429223, "uuid": "8d22dac4-f7c1-4d2e-8ce5-ead1fb007749", "historyId": "dbbfb647d41e9be86451bc17d071dcf3", "testCaseId": "dbbfb647d41e9be86451bc17d071dcf3", "fullName": "tests.container.test_containers_comprehensive.TestPostgresSessionManager#test_postgres_session_manager_init", "labels": [{"name": "feature", "value": "PostgresSessionManager"}, {"name": "story", "value": "Database Session Management"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}