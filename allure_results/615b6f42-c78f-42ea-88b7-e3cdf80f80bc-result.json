{"name": "initiative_attribute: test upsert with conflict condition", "status": "passed", "description": "\n        Test with_conflict_condition for upsert functionality for InitiativeAttribute table.\n        ", "attachments": [{"name": "stdout", "source": "d5e510ba-ab89-4224-8b5a-74be51b19821-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "test_data", "value": "[[10346, 'TRAIN-1', '2020-03-23', '2022-03-21', 'Managed Services'], [10346, 'TRAIN-1', '2020-03-23', '2019-03-21', 'Managed Services'], [10346, 'TRAIN-1', '2020-03-23', '2020-03-23', 'Managed Services']]"}, {"name": "conflict_condition", "value": "<sqlalchemy.sql.elements.BinaryExpression object at 0x00000136214A7140>"}, {"name": "expected_updated", "value": "[Timestamp('2022-03-21 00:00:00+0530', tz='Asia/Kolkata'), None, None]"}, {"name": "should_update", "value": "[True, False, False]"}], "start": 1751105564763, "stop": 1751105564801, "uuid": "da2c63a6-b59e-4ee9-ab72-625a47fe1384", "historyId": "66a2d0971af185e0c8aa9dc70cc3295d", "testCaseId": "eca30d1044a42541a27f98f1986608cc", "fullName": "tests.upsert_conflict_condition.test_upsert_initiative_attribute.TestUpsertInitiativeAttribute#test_upsert_with_conflict_condition_initiative_attribute", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "Test upsert with conflict condition for initiative_attribute table"}, {"name": "feature", "value": "Upsert Conflict Condition"}, {"name": "requirement", "value": "AIR-51"}, {"name": "parentSuite", "value": "tests.upsert_conflict_condition"}, {"name": "suite", "value": "test_upsert_initiative_attribute"}, {"name": "subSuite", "value": "TestUpsertInitiativeAttribute"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.upsert_conflict_condition.test_upsert_initiative_attribute"}]}