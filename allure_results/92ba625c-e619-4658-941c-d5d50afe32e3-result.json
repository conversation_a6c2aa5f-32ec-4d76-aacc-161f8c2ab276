{"name": "test_safe_response_access_patterns", "status": "passed", "description": "Test that our fixed code patterns safely access response data", "attachments": [{"name": "stdout", "source": "9b32f776-ccef-46db-b141-7866102cb2e5-attachment.txt", "type": "text/plain"}], "start": 1751105432113, "stop": 1751105432114, "uuid": "fcf08bff-db19-4f83-99e7-653584a07deb", "historyId": "11089639a12418c3ddda10daa9f6c1eb", "testCaseId": "11089639a12418c3ddda10daa9f6c1eb", "fullName": "tests.test_fetch_with_retries_consistency#test_safe_response_access_patterns", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_fetch_with_retries_consistency"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_fetch_with_retries_consistency"}]}