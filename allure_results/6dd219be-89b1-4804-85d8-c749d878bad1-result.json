{"name": "Test EntryDetailsBuilder pattern", "status": "passed", "description": "Test the builder pattern for EntryDetails.", "start": 1751105429212, "stop": 1751105429212, "uuid": "58d08c8f-655f-421a-96c3-e497a50cdc2f", "historyId": "15084eed0a800e1f870b7019359a9d45", "testCaseId": "15084eed0a800e1f870b7019359a9d45", "fullName": "tests.container.test_containers_comprehensive.TestEntryDetails#test_entry_details_builder", "labels": [{"name": "feature", "value": "EntryDetails"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "Entry Details Management"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestEntryDetails"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}