{"name": "Test schema_translate_map update to different schema", "status": "passed", "description": "Test that `update_schema` correctly updates the schema_translate_map on the engines.", "start": 1751105419845, "stop": 1751105421272, "uuid": "271177b8-e004-4be4-889f-d27d1ead8f30", "historyId": "00d7e3ca7cc4bd50d4907d070b1bfcac", "testCaseId": "00d7e3ca7cc4bd50d4907d070b1bfcac", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_update_schema_updates_schema_translate_map", "labels": [{"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Test against real db"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}