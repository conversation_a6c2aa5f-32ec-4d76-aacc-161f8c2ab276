Would verify that <MagicMock name='mock.fetchone().project' id='1735076542224'> == Managed Services for column project
Would convert timezone for updated: <MagicMock name='mock.fetchone().updated' id='1735076544048'> to Asia/Kolkata
Would verify that <MagicMock name='mock.fetchone().updated' id='1735076544048'> == 2022-03-21 00:00:00+05:30 for column updated
Would verify that <MagicMock name='mock.fetchone().project' id='1735075844256'> == Managed Services for column project
Would verify that <MagicMock name='mock.fetchone().updated' id='1735075846080'> == mocked_scalar_value for column updated
Would verify that <MagicMock name='mock.fetchone().project' id='1735075684112'> == Managed Services for column project
Would verify that <MagicMock name='mock.fetchone().updated' id='1735075682288'> == mocked_scalar_value for column updated
