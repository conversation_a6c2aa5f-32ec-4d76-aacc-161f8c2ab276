{"name": "Test schema_rw entry details", "status": "passed", "description": "Test that the schema_rw resolves correctly with the appropriate title.", "start": 1751105547915, "stop": 1751105547922, "uuid": "87dd5068-e21e-4399-88ce-6bc117175f16", "historyId": "6a1e3c252f59c65c9754e994ae43f9ae", "testCaseId": "6a1e3c252f59c65c9754e994ae43f9ae", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_schema_rw_entry_details", "labels": [{"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "minor"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}