{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751105425500, "stop": 1751105425501}, {"name": "Extract field IDs for datatype 'number'", "status": "passed", "start": 1751105425501, "stop": 1751105425501}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751105425501, "stop": 1751105425502}], "parameters": [{"name": "datatype", "value": "'number'"}, {"name": "expected_ids", "value": "['aggregatetimeestimate', 'aggregatetimeoriginalestimate', 'aggregatetimespent', 'customfield_10024', 'customfield_10120', 'customfield_10121', 'customfield_10122', 'customfield_10123', 'customfield_10124', 'customfield_10125', 'customfield_10126', 'customfield_10147', 'customfield_10199', 'timeestimate', 'timeoriginalestimate', 'timespent']"}], "start": 1751105425500, "stop": 1751105425503, "uuid": "09945fb6-3f15-412c-aaab-739a385ea2b9", "historyId": "4dc42aa1d31379f6e9bd925c8c29cc9c", "testCaseId": "77a2f6d4d2ec5a0725fb029808f275a1", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}