{"name": "test_container_composition", "status": "passed", "description": "Test container composition pattern.", "attachments": [{"name": "stdout", "source": "65b00352-428d-43cf-a5d0-9f50945bfa3c-attachment.txt", "type": "text/plain"}], "start": 1751105436038, "stop": 1751105436042, "uuid": "e214b3fa-1225-471b-a06a-903fd2d1c312", "historyId": "2778b8c20bf5d29fc75de5044277354a", "testCaseId": "2778b8c20bf5d29fc75de5044277354a", "fullName": "tests.test_logger_refactor#test_container_composition", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}