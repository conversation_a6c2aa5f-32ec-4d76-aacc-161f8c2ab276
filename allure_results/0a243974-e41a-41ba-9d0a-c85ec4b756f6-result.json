{"name": "test_logger_container", "status": "passed", "description": "Test LoggerContainer initialization and functionality.", "attachments": [{"name": "stdout", "source": "c4c877a1-0294-4c72-8500-6bbbfc02f90e-attachment.txt", "type": "text/plain"}], "start": 1751105564644, "stop": 1751105564709, "uuid": "9df27ce5-8e88-4c25-b880-5e956f0a7231", "historyId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "testCaseId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "fullName": "tests.test_logger_refactor#test_logger_container", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}