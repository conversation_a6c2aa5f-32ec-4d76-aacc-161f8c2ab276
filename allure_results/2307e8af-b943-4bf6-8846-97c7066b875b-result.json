{"name": "Test schema update functionality", "status": "passed", "description": "Test schema update functionality.", "start": 1751105429228, "stop": 1751105429232, "uuid": "eaf90240-b542-482a-a67a-5c2a1dac4153", "historyId": "8b22328d963c4804535f398ee225f666", "testCaseId": "8b22328d963c4804535f398ee225f666", "fullName": "tests.container.test_containers_comprehensive.TestPostgresSessionManager#test_update_schema", "labels": [{"name": "feature", "value": "PostgresSessionManager"}, {"name": "story", "value": "Database Session Management"}, {"name": "severity", "value": "critical"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestPostgresSessionManager"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}