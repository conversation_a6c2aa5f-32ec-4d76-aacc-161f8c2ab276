{"name": "Verify field IDs for a specific datatype", "status": "passed", "steps": [{"name": "Resolve FieldNameExtractor from the container", "status": "passed", "start": 1751105425514, "stop": 1751105425514}, {"name": "Extract field IDs for datatype 'user'", "status": "passed", "start": 1751105425514, "stop": 1751105425514}, {"name": "Verify the field IDs match the expected values", "status": "passed", "start": 1751105425514, "stop": 1751105425515}], "parameters": [{"name": "datatype", "value": "'user'"}, {"name": "expected_ids", "value": "['assignee', 'reporter']"}], "start": 1751105425514, "stop": 1751105425521, "uuid": "2e77698f-1ffd-42c1-861a-b46cc18b4a60", "historyId": "4a950522cf01223501e3fca0bf01ff55", "testCaseId": "77a2f6d4d2ec5a0725fb029808f275a1", "fullName": "tests.container.issue_fields_container.test_issue_fields_container.TestFieldNameExtractor#test_get_field_ids_by_datatype", "labels": [{"name": "story", "value": "Extract field IDs by datatype"}, {"name": "feature", "value": "FieldNameExtractor"}, {"name": "parentSuite", "value": "tests.container.issue_fields_container"}, {"name": "suite", "value": "test_issue_fields_container"}, {"name": "subSuite", "value": "TestFieldNameExtractor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.issue_fields_container.test_issue_fields_container"}]}