{"name": "Test database driver imports", "status": "passed", "description": "Test that database drivers can be imported.", "attachments": [{"name": "stdout", "source": "de2f74d1-bb0e-414b-84fc-b286c885a978-attachment.txt", "type": "text/plain"}], "start": 1751105429154, "stop": 1751105429155, "uuid": "ab41ca90-1e9a-407d-9aab-de6ab6afdf97", "historyId": "774c3dcbd1522e2d23647c68ce8e8171", "testCaseId": "774c3dcbd1522e2d23647c68ce8e8171", "fullName": "tests.container.test_basic_setup.TestBasicSetup#test_database_driver_imports", "labels": [{"name": "severity", "value": "blocker"}, {"name": "story", "value": "Environment Verification"}, {"name": "feature", "value": "Basic Setup"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_basic_setup"}, {"name": "subSuite", "value": "TestBasicSetup"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_basic_setup"}]}