{"name": "Test get_jira_users using DI fixture", "status": "passed", "description": "Test using the DI fixture for clean dependency injection setup", "attachments": [{"name": "stdout", "source": "c8e6f514-aa8a-4591-a1f9-db84f8a9f94e-attachment.txt", "type": "text/plain"}], "start": 1751105563142, "stop": 1751105563172, "uuid": "502da53f-1263-4704-b94f-5aa461e94711", "historyId": "eb1ed2d1620695a227b7bfdee21dea80", "testCaseId": "eb1ed2d1620695a227b7bfdee21dea80", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_with_fixture", "labels": [{"name": "feature", "value": "Jira API"}, {"name": "story", "value": "Get Jira Users"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}