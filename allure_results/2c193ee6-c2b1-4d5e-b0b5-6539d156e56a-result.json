{"name": "Test psycopg2 driver availability", "status": "passed", "description": "Test that psycopg2 driver is available and importable.", "attachments": [{"name": "stdout", "source": "d853b7e2-65bc-457c-b91c-fe6e830b629b-attachment.txt", "type": "text/plain"}], "start": 1751105549539, "stop": 1751105549540, "uuid": "2c10d179-2c67-4887-af5f-b5b6d6bced40", "historyId": "f22da130b98ff04199cec12f474d4841", "testCaseId": "f22da130b98ff04199cec12f474d4841", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_psycopg2_available", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Database Drivers"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}