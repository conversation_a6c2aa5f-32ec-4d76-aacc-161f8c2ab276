{"name": "Test lifecycle manager provider", "status": "passed", "description": "Test lifecycle manager provider.", "start": 1751105429636, "stop": 1751105429753, "uuid": "00b118c8-0fb7-4184-b299-695271f93275", "historyId": "f4f8ba0f84fae9fa606bec72097d0ce3", "testCaseId": "f4f8ba0f84fae9fa606bec72097d0ce3", "fullName": "tests.container.test_containers_comprehensive.TestApplicationContainer#test_lifecycle_manager_provider", "labels": [{"name": "story", "value": "Application-wide Dependency Injection"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_containers_comprehensive"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_containers_comprehensive"}]}