{"name": "Test KeePassManager instantiation", "status": "passed", "description": "Test the instantiation of the KeePass manager with the correct parameters.", "start": 1751105543946, "stop": 1751105547862, "uuid": "2fd73c1b-fac8-4f48-883b-65409e33b58a", "historyId": "03e0338a03b26223e82cbdc972372012", "testCaseId": "03e0338a03b26223e82cbdc972372012", "fullName": "tests.container.keepass_container.test_keepass_container.TestKeePassContainer#test_keepass_manager", "labels": [{"name": "feature", "value": "<PERSON>ePassC<PERSON>r"}, {"name": "story", "value": "Test KeePass manager and entry details resolution"}, {"name": "severity", "value": "normal"}, {"name": "parentSuite", "value": "tests.container.keepass_container"}, {"name": "suite", "value": "test_keepass_container"}, {"name": "subSuite", "value": "Test<PERSON><PERSON>Pass<PERSON><PERSON><PERSON>"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.keepass_container.test_keepass_container"}]}