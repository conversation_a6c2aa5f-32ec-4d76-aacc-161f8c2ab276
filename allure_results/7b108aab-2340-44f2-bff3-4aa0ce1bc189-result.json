{"name": "Test pg_ro_entry resolution with different schema", "status": "passed", "description": "Test that `pg_ro_entry` resolves with a different schema.", "start": 1751105536916, "stop": 1751105538200, "uuid": "5d86f321-dd91-48a6-898a-d4998378b708", "historyId": "c8385bdffc6c4da80da5dafadb94e832", "testCaseId": "c8385bdffc6c4da80da5dafadb94e832", "fullName": "tests.container.application_container.test_application_container_real_db.TestApplicationContainer#test_pg_ro_entry_different_schema", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Test against real db"}, {"name": "feature", "value": "ApplicationContainer"}, {"name": "parentSuite", "value": "tests.container.application_container"}, {"name": "suite", "value": "test_application_container_real_db"}, {"name": "subSuite", "value": "TestApplicationContainer"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.application_container.test_application_container_real_db"}]}