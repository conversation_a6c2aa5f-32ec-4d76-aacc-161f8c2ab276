{"name": "test_logger_container", "status": "passed", "description": "Test LoggerContainer initialization and functionality.", "attachments": [{"name": "stdout", "source": "de4220a6-e403-4500-828d-7e9558ce4be6-attachment.txt", "type": "text/plain"}], "start": 1751105436008, "stop": 1751105436031, "uuid": "3bad78f4-3aec-4a31-899c-1e2b7c7b4935", "historyId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "testCaseId": "e9fcea53dfb0f0363d96b6f5e0e923b8", "fullName": "tests.test_logger_refactor#test_logger_container", "labels": [{"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_logger_refactor"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_logger_refactor"}]}