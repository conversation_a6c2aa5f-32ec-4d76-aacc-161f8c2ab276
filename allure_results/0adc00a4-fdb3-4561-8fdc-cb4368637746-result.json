{"name": "test_failed_fetch_returns_exception", "status": "passed", "description": "C:\\Users\\<USER>\\PycharmProjects\\airflow\\tests\\features\\fetch_with_retries.feature: Failed fetch returns exception", "attachments": [{"name": "stdout", "source": "acb61807-738a-4941-9761-7c359ef67f08-attachment.txt", "type": "text/plain"}], "start": 1751105551787, "stop": 1751105551799, "uuid": "0e9cc376-8a65-4885-a2b8-e4267e505f8c", "historyId": "a882191d61a39582b9f699bc1ec128e0", "testCaseId": "a882191d61a39582b9f699bc1ec128e0", "fullName": "tests.steps.test_fetch_steps#test_failed_fetch_returns_exception", "labels": [{"name": "parentSuite", "value": "tests.steps"}, {"name": "suite", "value": "test_fetch_steps"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "14944-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.steps.test_fetch_steps"}]}