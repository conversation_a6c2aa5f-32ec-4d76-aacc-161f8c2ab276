{"name": "Test get_jira_users using DI fixture", "status": "passed", "description": "Test using the DI fixture for clean dependency injection setup", "attachments": [{"name": "stdout", "source": "f7b176fd-abaa-4c96-853b-acdf0de3447f-attachment.txt", "type": "text/plain"}], "start": 1751105434671, "stop": 1751105434681, "uuid": "a9bd2fa6-da53-4c68-bdc6-aa32e0d8a64c", "historyId": "eb1ed2d1620695a227b7bfdee21dea80", "testCaseId": "eb1ed2d1620695a227b7bfdee21dea80", "fullName": "tests.test_jira_api_mocks.TestJiraUsers#test_get_jira_users_with_fixture", "labels": [{"name": "story", "value": "Get Jira Users"}, {"name": "feature", "value": "Jira API"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_jira_api_mocks"}, {"name": "subSuite", "value": "TestJiraUsers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_jira_api_mocks"}]}