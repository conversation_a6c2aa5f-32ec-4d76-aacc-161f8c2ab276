{"name": "Test SQLAlchemy URL creation with asyncpg", "status": "passed", "description": "Test SQLAlchemy URL creation with asyncpg driver.", "attachments": [{"name": "stdout", "source": "b2965852-c8a8-466a-a121-f67f70b8e6f9-attachment.txt", "type": "text/plain"}], "start": 1751105429923, "stop": 1751105429924, "uuid": "bf24b789-07f8-4142-8cfd-af622f408178", "historyId": "b5d3023347e258e1f7d0fb7d81424f25", "testCaseId": "b5d3023347e258e1f7d0fb7d81424f25", "fullName": "tests.container.test_database_drivers.TestDatabaseDrivers#test_sqlalchemy_url_asyncpg", "labels": [{"name": "feature", "value": "Database Drivers"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "Driver Compatibility"}, {"name": "parentSuite", "value": "tests.container"}, {"name": "suite", "value": "test_database_drivers"}, {"name": "subSuite", "value": "TestDatabaseDrivers"}, {"name": "host", "value": "LAP-VISHALB"}, {"name": "thread", "value": "8604-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.container.test_database_drivers"}]}